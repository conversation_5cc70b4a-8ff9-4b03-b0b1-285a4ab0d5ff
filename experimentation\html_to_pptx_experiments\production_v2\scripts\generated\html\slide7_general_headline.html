<!DOCTYPE html>
<html>
<head>
<title>Realizing Significant Cost Reductions with AWS</title>
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
<script src="https://cdn.tailwindcss.com"></script>
<style>
  html, body {
    margin: 0;
    padding: 0;
    background-color: #f0f2f5;
    font-family: 'Roboto', sans-serif;
  }

  .slide-container {
    width: 1280px;
    height: 720px;
    background-color: #ffffff;
    color: #0d1b2a;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    padding: 60px 80px;
  }

  .slide-header {
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 3px solid #FF9900; /* AWS Orange */
    padding-bottom: 20px;
    margin-bottom: 28px;
  }

  .slide-header img {
    height: 50px;
    margin-right: 20px;
  }

  .slide-header h1 {
    font-size: 2.4em;
    font-weight: 700;
    color: #232F3E; /* AWS Dark Blue/Grey */
    margin: 0;
    line-height: 1.2;
  }

  .slide-content {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    gap: 40px;
    box-sizing: border-box;
  }

  .left-column {
    flex: 1 1 56%;
    min-width: 0;
  }

  .right-column {
    flex: 1 1 44%;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  h2 {
    font-size: 1.6em;
    font-weight: 700;
    color: #232F3E;
    margin: 0 0 12px 0;
  }

  p, li, td, th {
    font-size: 1.2em; /* Regular text size */
    line-height: 1.6;
    color: #545B64; /* AWS Grey */
  }

  ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
  }

  .check-list li {
    position: relative;
    padding-left: 28px;
    margin-bottom: 10px;
  }

  .check-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 0;
    color: #FF9900;
    font-weight: 700;
  }

  .quote-box {
    border-left: 4px solid #FF9900;
    background: #F9FAFB;
    padding: 14px 16px;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .quote-text {
    color: #0d1b2a;
    font-weight: 700;
    margin: 0;
  }

  .table-wrap {
    width: 100%;
    overflow: hidden;
    border: 1px solid #E5E7EB;
    border-radius: 10px;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
  }

  thead th {
    background: #F3F4F6;
    color: #232F3E;
    text-align: left;
    padding: 12px 14px;
    border-bottom: 1px solid #E5E7EB;
  }

  tbody td {
    padding: 12px 14px;
    vertical-align: top;
    border-bottom: 1px solid #E5E7EB;
  }

  tbody tr:last-child td {
    border-bottom: none;
  }

  .badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 9999px;
    font-size: 0.9em;
    font-weight: 700;
    color: #232F3E;
    background: #FEF3C7; /* light AWS orange tone */
    border: 1px solid #F59E0B;
  }

  .contact-card {
    border: 1px solid #E5E7EB;
    border-radius: 10px;
    padding: 14px 16px;
    background: #FFFFFF;
  }

  .contact-row {
    display: flex;
    gap: 14px;
    flex-wrap: wrap;
  }

  .contact-item {
    font-size: 1.1em;
    color: #374151;
  }

  .logo-box {
    width: 180px;
    height: 60px;
    border: 1px dashed #A7B1C2;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6B7280;
    font-weight: 700;
    margin-top: 10px;
  }

  .subhead {
    font-size: 1.3em;
    font-weight: 700;
    color: #232F3E;
    margin: 12px 0 8px 0;
  }
</style>
</head>
<body>
  <div class="slide-container">
    <div class="slide-header">
      <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/aws.svg" alt="AWS Logo">
      <h1>Realizing Significant Cost Reductions with AWS</h1>
    </div>

    <div class="slide-content">
      <!-- Left: Projection + Savings Table -->
      <div class="left-column">
        <div class="quote-box">
          <p class="quote-text">“Based on our analysis, we project that you can reduce your cloud spending by 15-30% within the first year by implementing these cost optimization strategies.”</p>
        </div>

        <h2>Projected Cost Savings</h2>
        <div class="table-wrap">
          <table aria-label="Projected Cost Savings Scenarios">
            <thead>
              <tr>
                <th style="width: 38%;">Scenario</th>
                <th style="width: 18%;">Projected Savings</th>
                <th style="width: 44%;">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Conservative Optimization</td>
                <td><span class="badge">15%</span></td>
                <td>Implementing basic cost-saving measures like deleting unused resources.</td>
              </tr>
              <tr>
                <td>Moderate Optimization</td>
                <td><span class="badge">22.5%</span></td>
                <td>Right-sizing instances and utilizing reserved instances.</td>
              </tr>
              <tr>
                <td>Aggressive Optimization</td>
                <td><span class="badge">30%</span></td>
                <td>Comprehensive optimization including automation, spot instances, and data tiering.</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Right: Next Steps + Contact -->
      <div class="right-column">
        <h2>Next Steps & Call to Action</h2>
        <ul class="check-list" style="margin-bottom: 16px;">
          <li>Schedule a follow-up meeting to discuss your specific needs and develop a customized cost optimization plan.</li>
          <li>Request a free AWS Well-Architected Review to identify potential areas for improvement.</li>
          <li>Visit our website to learn more about our AWS consulting services.</li>
        </ul>

        <div class="subhead">Contact Information & Company Logo</div>
        <div class="contact-card">
          <div class="contact-row" style="margin-bottom: 8px;">
            <div class="contact-item"><strong>Name:</strong> [Your Name]</div>
            <div class="contact-item"><strong>Title:</strong> [Your Title]</div>
          </div>
          <div class="contact-row" style="margin-bottom: 8px;">
            <div class="contact-item"><strong>Email:</strong> [<EMAIL>]</div>
            <div class="contact-item"><strong>Phone:</strong> [+****************]</div>
          </div>
          <div class="contact-row">
            <div class="contact-item"><strong>Website:</strong> [yourcompany.com]</div>
          </div>
          <div class="logo-box" aria-label="Company Logo Placeholder">Your Logo</div>
          <p style="font-size: 0.95em; color: #6B7280; margin: 10px 0 0 0;">[Your Contact Information and Company Logo Here]</p>
        </div>
      </div>
    </div>
  </div>
</body>
</html>