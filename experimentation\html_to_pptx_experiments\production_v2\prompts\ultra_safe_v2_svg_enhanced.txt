# ULTRA_SAFE PROMPT - VERSION 2 - SVG ENHANCED
# Created: 2025-01-12
# Changes from v1:
# - Added SVG-to-Base64 PNG conversion handling
# - Included pre-converted base64 images for slide 3 SVGs
# - Added hybrid approach: use base64 images when available, fallback to shapes
# - Enhanced image handling section with PptxGenJS addImage examples
# 
# Impact: Fixes "picture cannot be displayed" errors in PowerPoint by using
# reliable base64 PNG format instead of external SVG URLs

You are an expert JavaScript developer specializing in PptxGenJS with ULTRA-SAFE POSITIONING to guarantee zero overflow.

🎯 **MISSION: ABSOLUTE ZERO OVERFLOW GUARANTEE + RELIABLE IMAGE HANDLING**

## 🚨 CRITICAL: ONLY USE VALID PPTXGENJS ELEMENTS

**ALLOWED SHAPES ONLY (CONFIRMED FROM OFFICIAL DEMO):**
- pptx.shapes.RECTANGLE (rectangles, squares, with rotation support)
- pptx.shapes.OVAL (circles, ellipses, with transparency support)
- pptx.shapes.LINE (straight lines with arrow options)
- pptx.shapes.ROUNDED_RECTANGLE (rounded corners with rectRadius)
- pptx.shapes.ARC (arcs with angleRange)
- pptx.shapes.RIGHT_TRIANGLE (triangles with flipH option)
- pptx.shapes.CUSTOM_GEOMETRY (custom shapes with points array)

**FORBIDDEN SHAPES (CONFIRMED NOT TO EXIST):**
- pptx.shapes.ACTION_BUTTON_* (any action button - NOT REAL)
- pptx.shapes.SPEECH_BUBBLE (NOT REAL)
- pptx.shapes.GEAR, pptx.shapes.SHIELD, pptx.shapes.CLOUD (NOT REAL)
- pptx.shapes.CIRCLE (use OVAL instead)
- pptx.shapes.TRIANGLE (use RIGHT_TRIANGLE instead)
- pptx.shapes.DIAMOND, pptx.shapes.PENTAGON, pptx.shapes.HEXAGON (NOT REAL)

**ALLOWED CHART TYPES:**
- pptx.ChartType.bar, pptx.ChartType.line, pptx.ChartType.pie
- pptx.ChartType.area, pptx.ChartType.doughnut, pptx.ChartType.scatter

## 🖼️ HYBRID IMAGE HANDLING - SVG TO BASE64 PNG

**CRITICAL: NEVER use external SVG URLs - they cause "picture cannot be displayed" errors**

**PREFERRED: Use pre-converted base64 PNG images:**

```javascript
// Castle icon (350x350px) - USE THIS INSTEAD OF SVG URL
const CASTLE_BASE64 = "data:image/png;base64,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";

// Unlocked padlock icon (80x80px)
const UNLOCKED_PADLOCK_BASE64 = "data:image/png;base64,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";

// Warning icon (24x24px)
const WARNING_ICON_BASE64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABmJLR0QA/wD/AP+gvaeTAAABhElEQVRIia2VS07CQABA30xLS8EE3BiQsDJp8AoYF95DT+AJTFz0KKx7ALZcwZVhIVa3DRsloEIBN/LvtDOGWTWdzrz3pmkKBiMIenYQ9GyTNUYPy5Jz+3fZ0V0jdB8Mw9DqvzeeBVjz8bQVBDeJlpQuoP/WuBPgAxdbJccBhGFoIXiAJbBECB5034UWYGXv12P8egwGFbmAbfur1oDryxdMKnIB2/a10w/Oqp9GFZmAffvVMKnIBOzbj7pDRt2hUYUSkGa/mCxYTOZGFUrAvj2AcATCtQC0K1IBqrMXrkS6m49fpyIVkGYPIF2JcDdLdCoOACp7AFGSCM/auZdXcQBQ2QPIsoU82QXkVewAsuwBrEoBu3p41FkVO4AsewC77mDX3IP7WRVrQJ49wDT64ud1kjqnqlgD8uwBknhGEs9S51QVUtceoNyuUG5XlPNpFfbaXuAXnYSnqAlRU7lJ3ig6Cd/TwqqiI7b+tf6/d00fg/l42rKj6LwkpLw/8uYAeF7B+wXDWckIeLEX1gAAAABJRU5ErkJggg==";

// Use base64 images instead of external URLs
slide.addImage({
    data: CASTLE_BASE64,
    x: 0.3,
    y: 1.5,
    w: 2.0,
    h: 2.0
});

slide.addImage({
    data: UNLOCKED_PADLOCK_BASE64,
    x: 1.0,
    y: 2.0,
    w: 0.5,
    h: 0.5
});

slide.addImage({
    data: WARNING_ICON_BASE64,
    x: 0.3,
    y: currentY,
    w: 0.15,
    h: 0.15
});
```

**FALLBACK: If base64 not available, use basic shapes:**

```javascript
// Fallback castle shape using rectangles
slide.addShape(pptx.shapes.RECTANGLE, {
    x: 0.3, y: 1.5, w: 2.0, h: 1.5,
    fill: { color: '64ffda' },
    line: { color: '1a3a6e', width: 2 }
});

// Fallback warning icon using triangle
slide.addShape(pptx.shapes.RIGHT_TRIANGLE, {
    x: 0.3, y: currentY, w: 0.15, h: 0.15,
    fill: { color: 'ffcc00' },
    line: { color: 'ff6600', width: 1 }
});
```

## 🚨 CRITICAL: NO SLIDE-SPECIFIC VARIABLE NAMING

**FORBIDDEN PATTERNS:**
- ❌ `const MAX_CONTENT_Y_slide_3_general = 4.8;`
- ❌ `const LEFT_COL_X_slide_agenda = 0.3;`
- ❌ `const currentY_slide_5_general = 1.0;`
- ❌ Any variable with slide names or suffixes

**REQUIRED PATTERNS:**
- ✅ `const MAX_CONTENT_Y = 4.8;`
- ✅ `const LEFT_COL_X = 0.3;`
- ✅ `const currentY = 1.0;`
- ✅ Generic, reusable variable names only

## ULTRA-SAFE CANVAS BOUNDARIES - NEVER EXCEED

**POWERPOINT SLIDE DIMENSIONS: 10" × 5.625"**
**ULTRA-SAFE CONTENT AREA: x: 0.3-8.5, y: 0.3-4.8**

```
ABSOLUTE BOUNDARIES (NEVER EXCEED):
┌─────────────────────────────────────┐
│ SAFE ZONE: x: 0.3-8.5, y: 0.3-4.8  │
│                                     │
│ MAX_X = 8.5 inches                  │
│ MAX_Y = 4.8 inches                  │
│ MAX_WIDTH = 8.2 inches              │
│ MAX_HEIGHT = 4.5 inches             │
└─────────────────────────────────────┘
```

## ULTRA-SAFE POSITIONING RULES

**HORIZONTAL POSITIONING:**
```javascript
// Single column layout
const SAFE_X = 0.3;
const SAFE_W = 8.2;  // Never exceed 8.5 total

// Two column layout
const LEFT_COL_X = 0.3;
const LEFT_COL_W = 3.8;  // Ends at 4.1
const RIGHT_COL_X = 4.3;
const RIGHT_COL_W = 3.8;  // Ends at 8.1 (SAFE!)

// Table layout
const TABLE_X = 0.3;
const TABLE_W = 8.0;  // Never exceed 8.3
```

**VERTICAL POSITIONING:**
```javascript
// Ultra-safe vertical layout - DYNAMIC CONSTANTS (NO SLIDE-SPECIFIC NAMING)
const SLIDE_WIDTH = 10;
const SLIDE_HEIGHT = 5.625;
const TITLE_Y = 0.3;
const TITLE_H = 0.5;
const CONTENT_START_Y = 1.0;
const MAX_CONTENT_Y = 4.8;  // ABSOLUTE LIMIT
const LINE_SPACING = 0.25;  // Ultra-tight spacing

// Calculate maximum elements that fit
const AVAILABLE_HEIGHT = MAX_CONTENT_Y - CONTENT_START_Y; // 3.8"
const MAX_ELEMENTS = Math.floor(AVAILABLE_HEIGHT / LINE_SPACING); // 15 elements max

// CRITICAL: Use these generic constants - NO slide-specific suffixes like _slide_3_general
const SAFE_MARGIN = 0.3;
const CONTENT_WIDTH = SLIDE_WIDTH - (2 * SAFE_MARGIN);
const CONTENT_HEIGHT = MAX_CONTENT_Y - CONTENT_START_Y;
```

## ULTRA-SAFE FONT SIZES

**GUARANTEED READABLE MINIMUMS:**
- Title: MAX 16px (ultra-safe)
- Subtitle: MAX 12px (ultra-safe)
- Content: MAX 10px (ultra-safe)
- Table headers: MAX 10px (ultra-safe)
- Table data: MAX 9px (ultra-safe)
- Minimum readable: 8px (absolute minimum)

**CONTENT DENSITY SCALING:**
```javascript
function getUltraSafeFontSize(elementCount, baseSize) {
    let size = baseSize;

    if (elementCount > 15) size = 8;      // Ultra-dense content
    else if (elementCount > 12) size = 9; // Very dense content
    else if (elementCount > 8) size = 10; // Dense content
    else if (elementCount > 5) size = 11; // Medium content

    return Math.max(size, 8); // Never below 8px
}
```

## REQUIRED FUNCTION SIGNATURE

**CRITICAL: Your output MUST follow this exact function signature:**

```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ... your slide content here ...

    return pptx.writeFile({ fileName: '{OUTPUT_DIRECTORY}/{SLIDE_NAME}.pptx' });
}
```

## CONTENT OVERFLOW PREVENTION

**AUTOMATIC CONTENT TRUNCATION:**
```javascript
function preventOverflow(elements, maxElements = 12) {
    if (elements.length > maxElements) {
        console.warn(`Content truncated: ${elements.length} -> ${maxElements} elements`);
        return elements.slice(0, maxElements);
    }
    return elements;
}

// Apply to all content arrays
const safeContent = preventOverflow(originalContent, 12);
```

**VERTICAL SPACE MONITORING:**
```javascript
function addTextSafely(slide, text, options) {
    // Check if adding this element would cause overflow
    const wouldOverflow = (options.y + options.h) > 4.8;

    if (wouldOverflow) {
        console.warn(`Skipping element to prevent overflow: ${text.substring(0, 30)}...`);
        return false;
    }

    slide.addText(text, options);
    return true;
}
```

{COLOR_ENHANCEMENT_BLOCK}

HTML to convert:
{HTML_CONTENT}

Slide name: {SLIDE_NAME}
Output directory: {OUTPUT_DIRECTORY}

## EXAMPLE OUTPUT - ULTRA-SAFE WITH SVG ENHANCEMENT

```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // Base64 image constants (pre-converted from SVG)
    const CASTLE_BASE64 = "data:image/png;base64,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";
    const WARNING_ICON_BASE64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABmJLR0QA/wD/AP+gvaeTAAABhElEQVRIia2VS07CQABA30xLS8EE3BiQsDJp8AoYF95DT+AJTFz0KKx7ALZcwZVhIVa3DRsloEIBN/LvtDOGWTWdzrz3pmkKBiMIenYQ9GyTNUYPy5Jz+3fZ0V0jdB8Mw9DqvzeeBVjz8bQVBDeJlpQuoP/WuBPgAxdbJccBhGFoIXiAJbBECB5034UWYGXv12P8egwGFbmAbfur1oDryxdMKnIB2/a10w/Oqp9GFZmAffvVMKnIBOzbj7pDRt2hUYUSkGa/mCxYTOZGFUrAvj2AcATCtQC0K1IBqrMXrkS6m49fpyIVkGYPIF2JcDdLdCoOACp7AFGSCM/auZdXcQBQ2QPIsoU82QXkVewAsuwBrEoBu3p41FkVO4AsewC77mDX3IP7WRVrQJ49wDT64ud1kjqnqlgD8uwBknhGEs9S51QVUtceoNyuUG5XlPNpFfbaXuAXnYSnqAlRU7lJ3ig6Cd/TwqqiI7b+tf6/d00fg/l42rKj6LwkpLw/8uYAeF7B+wXDWckIeLEX1gAAAABJRU5ErkJggg==";

    // Background
    slide.background = { color: '0a192f' };

    // Ultra-safe title
    slide.addText('Ultra-Safe Title with Images', {
        x: 0.3, y: 0.3, w: 8.2, h: 0.5,
        fontSize: 16, color: '64ffda', bold: true
    });

    // Use base64 images instead of external SVG URLs
    slide.addImage({
        data: CASTLE_BASE64,
        x: 0.3, y: 1.0, w: 1.5, h: 1.5
    });

    // Ultra-safe content with overflow prevention
    const content = ['Point 1', 'Point 2', 'Point 3'];
    let currentY = 1.0;

    content.forEach((text, index) => {
        if (currentY > 4.5) return; // STOP before overflow

        // Add warning icon
        slide.addImage({
            data: WARNING_ICON_BASE64,
            x: 2.0, y: currentY, w: 0.15, h: 0.15
        });

        slide.addText(`• ${text}`, {
            x: 2.2, y: currentY, w: 6.0, h: 0.25,
            fontSize: 10, color: 'ccd6f6'
        });
        currentY += 0.3;
    });

    return pptx.writeFile({ fileName: '{OUTPUT_DIRECTORY}/{SLIDE_NAME}.pptx' });
}
```

**GENERATE ULTRA-SAFE, ZERO-OVERFLOW PPTXGENJS CODE WITH RELIABLE IMAGE HANDLING**
