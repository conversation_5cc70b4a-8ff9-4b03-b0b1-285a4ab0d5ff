You are an expert JavaScript developer specializing in PptxGenJS. Convert this HTML slide to PptxGenJS code with BALANCED SIZING - readable fonts that prevent overflow.

🎯 **MISSION: READABLE FONTS + NO OVERFLOW**

## BALANCED FONT SIZES - READABLE BUT SAFE

**OPTIMAL FONT SIZES (BALANCED):**
- Title (H1): 22px (readable but not huge)
- Subtitle (H2): 16px (clear but compact)
- Content (H3): 12px (readable standard)
- Body text (P): 11px (readable minimum)
- Bullet points: 11px (readable minimum)

**SMART RESPONSIVE SIZING:**
- If text length > 80 chars → reduce font size by 2px
- If text length > 120 chars → reduce font size by 3px
- If more than 6 elements → reduce ALL fonts by 1px
- If more than 8 elements → reduce ALL fonts by 2px

## SMART POSITIONING - PREVENT OVERFLOW

**SLIDE DIMENSIONS: 10" × 5.625"**
**SAFE CONTENT AREA: x: 0.4-9.2, y: 0.4-5.0**

**BALANCED VERTICAL SPACING:**
```
TITLE_ZONE: y: 0.4, h: 0.7 (balanced)
CONTENT_START: y: 1.2 (reasonable)
LINE_SPACING: 0.35" between elements (balanced)
BULLET_SPACING: 0.3" between bullets (readable)
MAX_Y_POSITION: 4.7 (safe limit)
```

**BALANCED TEXT WIDTHS:**
- Single column: w: 8.6 (good usage)
- Two columns: w: 4.0 each (balanced)
- Always leave reasonable margins

## CONTENT ANALYSIS & SMART SIZING

**STEP 1: CONTENT DENSITY ANALYSIS**
```javascript
const total_elements = count_all_text_blocks();
let title_size = 22;
let content_size = 12;
let bullet_size = 11;

// Adjust based on content density
if (total_elements > 8) {
    title_size = 20;
    content_size = 11;
    bullet_size = 10;
} else if (total_elements > 6) {
    title_size = 21;
    content_size = 11;
    bullet_size = 10;
}
```

**STEP 2: TEXT LENGTH OPTIMIZATION**
```javascript
function getOptimalFontSize(text, baseSize) {
    let size = baseSize;
    if (text.length > 80) size -= 2;
    if (text.length > 120) size -= 1;
    return Math.max(size, 9); // Never go below 9px (readable minimum)
}
```

**STEP 3: VERTICAL SPACE MANAGEMENT**
```javascript
// Calculate required space and adjust if needed
const available_height = 4.3;
const estimated_height = total_elements * 0.35;
if (estimated_height > available_height) {
    // Reduce spacing slightly, not font sizes
    line_spacing = 0.3;
    bullet_spacing = 0.25;
}
```

## BALANCED BULLET POINTS

**READABLE BULLET LAYOUT:**
```javascript
// Balanced bullet points with good spacing
const bullets = [
    { icon: '✓', text: 'Readable bullet text' },
    { icon: '✓', text: 'Another clear point' }
];

let currentY = 1.2;
bullets.forEach(bullet => {
    const fontSize = getOptimalFontSize(bullet.text, 11);
    slide.addText(`${bullet.icon} ${bullet.text}`, {
        x: 0.4, y: currentY, w: 8.6, h: 0.3,
        fontSize: fontSize,
        color: 'EXTRACTED_COLOR'
    });
    currentY += 0.35; // Balanced spacing
});
```

**SMART TEXT WRAPPING:**
```javascript
// Only split text if it's extremely long (>100 chars)
function smartTextSplit(text, maxLength = 100) {
    if (text.length <= maxLength) return [text];
    
    // Find natural break points
    const sentences = text.split('. ');
    if (sentences.length > 1 && sentences[0].length < maxLength) {
        return sentences.map(s => s.endsWith('.') ? s : s + '.');
    }
    
    // Fall back to word wrapping
    const words = text.split(' ');
    const lines = [];
    let currentLine = '';
    
    words.forEach(word => {
        if ((currentLine + word).length > maxLength) {
            lines.push(currentLine.trim());
            currentLine = word + ' ';
        } else {
            currentLine += word + ' ';
        }
    });
    
    if (currentLine.trim()) lines.push(currentLine.trim());
    return lines;
}
```

## COLOR EXTRACTION (UNCHANGED)
- Extract colors exactly from HTML
- Remove # from hex colors
- Use fallback colors if extraction fails

HTML to convert:
{HTML_CONTENT}

Slide name: {SLIDE_NAME}
Output directory: {OUTPUT_DIRECTORY}

## REQUIRED FUNCTION SIGNATURE

**CRITICAL: Your output MUST follow this exact function signature:**

```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ... your slide content here ...

    return pptx.writeFile({ fileName: '{OUTPUT_DIRECTORY}/{SLIDE_NAME}.pptx' });
}
```

**REQUIREMENTS:**
- Function name MUST be exactly `createPresentation`
- Function takes NO parameters
- Function creates its own `pptx` object
- Function MUST return `pptx.writeFile()` promise
- Do NOT include import/require statements

## EXAMPLE OUTPUT - BALANCED APPROACH

```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();
    
    // Background
    slide.background = { color: 'EXTRACTED_BG_COLOR' };
    
    // Balanced title - readable but not huge
    const titleText = 'Your Title Here';
    const titleSize = getOptimalFontSize(titleText, 22);
    slide.addText(titleText, {
        x: 0.4, y: 0.4, w: 8.6, h: 0.7,
        fontSize: titleSize, color: 'EXTRACTED_TITLE_COLOR', bold: true
    });
    
    let currentY = 1.2; // Reasonable start position
    
    // Balanced content with good spacing
    const content = [
        'First point with readable text',
        'Second point also readable',
        'Third point with balanced spacing'
    ];
    
    content.forEach(text => {
        const fontSize = getOptimalFontSize(text, 11);
        slide.addText(`✓ ${text}`, {
            x: 0.4, y: currentY, w: 8.6, h: 0.3,
            fontSize: fontSize, color: 'EXTRACTED_COLOR'
        });
        currentY += 0.35; // Balanced spacing
    });
    
    return pptx.writeFile({ fileName: '{OUTPUT_DIRECTORY}/{SLIDE_NAME}.pptx' });
}

// Helper function for optimal sizing
function getOptimalFontSize(text, baseSize) {
    let size = baseSize;
    if (text.length > 80) size -= 2;
    if (text.length > 120) size -= 1;
    return Math.max(size, 9); // Readable minimum
}
```

## QUALITY CHECKLIST
- [ ] Title font ≥ 20px (readable)
- [ ] Content font ≥ 10px (readable)
- [ ] Bullet font ≥ 9px (readable minimum)
- [ ] All text within y: 0.4-4.7
- [ ] Line spacing ≥ 0.3" (readable)
- [ ] Text not split unless >100 chars
- [ ] All content fits without overflow
- [ ] Fonts remain readable

**GENERATE BALANCED, READABLE, OVERFLOW-FREE PPTXGENJS CODE**
