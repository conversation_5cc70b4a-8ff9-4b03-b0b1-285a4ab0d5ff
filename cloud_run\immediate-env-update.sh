#!/bin/bash

# Immediate environment variable update for existing Cloud Run service
# This updates the current running service without rebuilding

PROJECT_ID="gen-lang-client-0822415637"
SERVICE_NAME="pptx-planner"
REGION="asia-northeast1"

echo "⚡ Immediately updating Cloud Run service environment variables..."
echo "Service: $SERVICE_NAME"
echo "Region: $REGION"

# Set the project
gcloud config set project $PROJECT_ID

# Get current environment variables to preserve them
echo "📋 Getting current environment variables..."
CURRENT_VARS=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(spec.template.spec.containers[0].env[].name,spec.template.spec.containers[0].env[].value)" | tr '\n' ',' | sed 's/,$//')

# Add the new environment variables
echo "📝 Adding new environment variables..."
gcloud run services update $SERVICE_NAME \
    --region $REGION \
    --set-env-vars "GCS_BUCKET_NAME=pptx-planner-storage,FIREBASE_PROJECT_ID=$PROJECT_ID,GOOGLE_CLOUD_PROJECT=$PROJECT_ID"

echo "✅ Environment variables updated immediately!"
echo ""
echo "📋 Current environment variables:"
gcloud run services describe $SERVICE_NAME --region=$REGION --format="table(spec.template.spec.containers[0].env[].name,spec.template.spec.containers[0].env[].value)"
echo ""
echo "🌐 Your service URL (unchanged):"
echo "https://pptx-planner-xes3mkf3ua-an.a.run.app"
echo ""
echo "⚠️  Important: This is a one-time update. To make it permanent:"
echo "1. Run ./update-cloud-build-trigger.sh to update your build trigger"
echo "2. Or manually add the env vars to your trigger in Cloud Console"
echo "3. Otherwise, the next deployment will overwrite these changes"
