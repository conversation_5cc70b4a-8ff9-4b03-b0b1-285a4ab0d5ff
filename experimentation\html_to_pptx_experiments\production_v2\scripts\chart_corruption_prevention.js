/**
 * Chart Corruption Prevention Utility
 * 
 * This module provides code-side validation to prevent PowerPoint corruption
 * caused by problematic PptxGenJS chart options. Use this as a final safety
 * check before generating charts.
 * 
 * Based on real-world corruption issues encountered with slide6_general_economics
 * where specific chart options caused PowerPoint to require repair and show
 * empty slides after repair.
 */

/**
 * Prevents chart corruption by removing problematic options
 * @param {Object} chartOptions - Chart options object to validate
 * @param {boolean} verbose - Whether to log detailed information
 * @returns {Object} - Cleaned chart options safe for PowerPoint generation
 */
function preventChartCorruption(chartOptions, verbose = true) {
    if (verbose) {
        console.log('🛡️ Applying chart corruption prevention...');
    }
    
    // Create a copy to avoid mutating the original
    const safeOptions = { ...chartOptions };
    const removedOptions = [];
    const warnings = [];
    
    // FORBIDDEN PATTERN 1: Options that cause silent corruption
    const forbiddenOptions = [
        'showValAxisGridLines',  // Invalid PptxGenJS option
        'valGridLine',           // Complex grid line objects not supported
        'catGridLine',           // Complex grid line objects not supported
        'plotArea',              // Advanced layout causes corruption
        'chartArea',             // Advanced styling causes corruption
        'chartColorsOpacity',    // Opacity options often cause issues
        'catAxisLabelColor',     // Axis label colors can be problematic
        'valAxisLabelColor',     // Axis label colors can be problematic
        'dataLabelBorder',       // Complex border objects cause issues
        'showCatAxisGridLines',  // Invalid option
        'catAxisGridLine'        // Complex objects cause issues
    ];
    
    forbiddenOptions.forEach(option => {
        if (safeOptions.hasOwnProperty(option)) {
            delete safeOptions[option];
            removedOptions.push(option);
        }
    });
    
    // FORBIDDEN PATTERN 2: lineDash arrays with null/undefined values
    if (safeOptions.lineDash) {
        const hasNullOrUndefined = safeOptions.lineDash.some(val => 
            val === null || val === undefined
        );
        
        if (hasNullOrUndefined) {
            delete safeOptions.lineDash;
            removedOptions.push('lineDash (contained null/undefined values)');
            warnings.push('lineDash with null/undefined values causes silent PowerPoint corruption');
        }
        
        // Check for mixed types (additional safety)
        const types = [...new Set(safeOptions.lineDash?.map(val => typeof val) || [])];
        if (types.length > 1) {
            delete safeOptions.lineDash;
            removedOptions.push('lineDash (mixed data types)');
            warnings.push('lineDash with mixed data types may cause corruption');
        }
    }
    
    // FORBIDDEN PATTERN 3: Color options with # prefix (can cause issues)
    if (safeOptions.chartColors) {
        const hasHashPrefix = safeOptions.chartColors.some(color => 
            typeof color === 'string' && color.startsWith('#')
        );
        
        if (hasHashPrefix) {
            safeOptions.chartColors = safeOptions.chartColors.map(color => 
                typeof color === 'string' && color.startsWith('#') ? color.substring(1) : color
            );
            warnings.push('Removed # prefix from chartColors for better compatibility');
        }
    }
    
    // VALIDATION: Check for positioning within ultra-safe bounds
    if (safeOptions.x !== undefined && (safeOptions.x < 0.3 || safeOptions.x > 8.5)) {
        warnings.push(`x position ${safeOptions.x} is outside ultra-safe bounds (0.3-8.5)`);
    }
    
    if (safeOptions.y !== undefined && (safeOptions.y < 0.3 || safeOptions.y > 4.8)) {
        warnings.push(`y position ${safeOptions.y} is outside ultra-safe bounds (0.3-4.8)`);
    }
    
    // VALIDATION: Check for reasonable chart dimensions
    if (safeOptions.w !== undefined && safeOptions.w > 8.0) {
        warnings.push(`Chart width ${safeOptions.w} may exceed slide bounds`);
    }
    
    if (safeOptions.h !== undefined && safeOptions.h > 4.0) {
        warnings.push(`Chart height ${safeOptions.h} may exceed slide bounds`);
    }
    
    // Log results
    if (verbose) {
        if (removedOptions.length > 0) {
            console.warn('🚨 Removed corruption-causing options:', removedOptions);
        }
        
        if (warnings.length > 0) {
            console.warn('⚠️ Warnings:', warnings);
        }
        
        if (removedOptions.length === 0 && warnings.length === 0) {
            console.log('✅ Chart options are already corruption-free');
        } else {
            console.log('✅ Chart options are now corruption-free');
        }
    }
    
    return safeOptions;
}

/**
 * Validates chart data structure for different chart types
 * @param {Array} chartData - Chart data array to validate
 * @param {string} chartType - Type of chart ('line', 'pie', 'bar')
 * @returns {Object} - Validation result with isValid boolean and errors array
 */
function validateChartData(chartData, chartType) {
    const errors = [];
    
    if (!Array.isArray(chartData)) {
        errors.push('Chart data must be an array');
        return { isValid: false, errors };
    }
    
    if (chartData.length === 0) {
        errors.push('Chart data array cannot be empty');
        return { isValid: false, errors };
    }
    
    if (chartType === 'line' || chartType === 'bar') {
        // Line and bar charts: each series should have name, labels, and values
        chartData.forEach((series, index) => {
            if (!series.name) {
                errors.push(`Series ${index}: missing 'name' property`);
            }
            
            if (!series.labels || !Array.isArray(series.labels)) {
                errors.push(`Series ${index}: missing or invalid 'labels' array`);
            }
            
            if (!series.values || !Array.isArray(series.values)) {
                errors.push(`Series ${index}: missing or invalid 'values' array`);
            }
            
            if (series.labels && series.values && 
                series.labels.length !== series.values.length) {
                errors.push(`Series ${index}: labels and values arrays must have same length`);
            }
        });
    }
    
    if (chartType === 'pie') {
        // Pie charts: should have single object with name, labels, and values
        if (chartData.length !== 1) {
            errors.push('Pie charts should have exactly one data object');
        }
        
        const pieData = chartData[0];
        if (!pieData.name) {
            errors.push('Pie chart: missing \'name\' property');
        }
        
        if (!pieData.labels || !Array.isArray(pieData.labels)) {
            errors.push('Pie chart: missing or invalid \'labels\' array');
        }
        
        if (!pieData.values || !Array.isArray(pieData.values)) {
            errors.push('Pie chart: missing or invalid \'values\' array');
        }
        
        if (pieData.labels && pieData.values && 
            pieData.labels.length !== pieData.values.length) {
            errors.push('Pie chart: labels and values arrays must have same length');
        }
    }
    
    return { isValid: errors.length === 0, errors };
}

/**
 * Complete chart safety check - validates both data and options
 * @param {Array} chartData - Chart data to validate
 * @param {Object} chartOptions - Chart options to clean
 * @param {string} chartType - Type of chart ('line', 'pie', 'bar')
 * @param {boolean} verbose - Whether to log detailed information
 * @returns {Object} - Result with safeOptions, dataValidation, and overall success
 */
function ensureChartSafety(chartData, chartOptions, chartType, verbose = true) {
    if (verbose) {
        console.log(`🔍 Performing complete chart safety check for ${chartType} chart...`);
    }
    
    // Validate chart data
    const dataValidation = validateChartData(chartData, chartType);
    
    // Clean chart options
    const safeOptions = preventChartCorruption(chartOptions, verbose);
    
    const result = {
        safeOptions,
        dataValidation,
        isReady: dataValidation.isValid,
        chartType
    };
    
    if (verbose) {
        if (result.isReady) {
            console.log('✅ Chart is ready for safe generation');
        } else {
            console.error('❌ Chart has validation errors:', dataValidation.errors);
        }
    }
    
    return result;
}

/**
 * Safe chart creation wrapper
 * @param {Object} slide - PptxGenJS slide object
 * @param {Object} pptx - PptxGenJS presentation object
 * @param {string} chartType - Type of chart ('line', 'pie', 'bar')
 * @param {Array} chartData - Chart data array
 * @param {Object} chartOptions - Chart options object
 * @param {boolean} verbose - Whether to log detailed information
 * @returns {boolean} - Success status
 */
function createSafeChart(slide, pptx, chartType, chartData, chartOptions, verbose = true) {
    try {
        // Perform complete safety check
        const safetyCheck = ensureChartSafety(chartData, chartOptions, chartType, verbose);
        
        if (!safetyCheck.isReady) {
            console.error('❌ Cannot create chart due to validation errors');
            return false;
        }
        
        // Create chart with safe options
        slide.addChart(pptx.ChartType[chartType], chartData, safetyCheck.safeOptions);
        
        if (verbose) {
            console.log(`✅ ${chartType} chart created successfully`);
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Chart creation failed:', error.message);
        return false;
    }
}

// Export functions for use in other modules
module.exports = {
    preventChartCorruption,
    validateChartData,
    ensureChartSafety,
    createSafeChart
};

// For browser environments
if (typeof window !== 'undefined') {
    window.ChartCorruptionPrevention = {
        preventChartCorruption,
        validateChartData,
        ensureChartSafety,
        createSafeChart
    };
}
