#!/bin/bash

# Update Cloud Build Trigger to include environment variables
# This modifies the trigger so future deployments will include the new env vars

PROJECT_ID="gen-lang-client-0822415637"
TRIGGER_ID="87b6256e-1f73-4759-a555-593752bd8bc7"

echo "🔧 Updating Cloud Build trigger to include environment variables..."
echo "Trigger ID: $TRIGGER_ID"

# Create a temporary trigger configuration file
cat > trigger-config.yaml << EOF
build:
  images:
  - \$_AR_HOSTNAME/\$_AR_PROJECT_ID/\$_AR_REPOSITORY/\$REPO_NAME/\$_SERVICE_NAME:\$COMMIT_SHA
  options:
    logging: CLOUD_LOGGING_ONLY
    substitutionOption: ALLOW_LOOSE
  steps:
  - args:
    - build
    - --no-cache
    - -t
    - \$_AR_HOSTNAME/\$_AR_PROJECT_ID/\$_AR_REPOSITORY/\$REPO_NAME/\$_SERVICE_NAME:\$COMMIT_SHA
    - .
    - -f
    - Dockerfile
    id: Build
    name: gcr.io/cloud-builders/docker
  - args:
    - push
    - \$_AR_HOSTNAME/\$_AR_PROJECT_ID/\$_AR_REPOSITORY/\$REPO_NAME/\$_SERVICE_NAME:\$COMMIT_SHA
    id: Push
    name: gcr.io/cloud-builders/docker
  - args:
    - run
    - services
    - update
    - \$_SERVICE_NAME
    - --platform=managed
    - --image=\$_AR_HOSTNAME/\$_AR_PROJECT_ID/\$_AR_REPOSITORY/\$REPO_NAME/\$_SERVICE_NAME:\$COMMIT_SHA
    - --labels=managed-by=gcp-cloud-build-deploy-cloud-run,commit-sha=\$COMMIT_SHA,gcb-build-id=\$BUILD_ID,gcb-trigger-id=\$_TRIGGER_ID
    - --region=\$_DEPLOY_REGION
    - --set-env-vars=GCS_BUCKET_NAME=pptx-planner-storage,FIREBASE_PROJECT_ID=$PROJECT_ID,GOOGLE_CLOUD_PROJECT=$PROJECT_ID
    - --quiet
    entrypoint: gcloud
    id: Deploy
    name: gcr.io/google.com/cloudsdktool/cloud-sdk:slim
  substitutions:
    _AR_HOSTNAME: asia-northeast1-docker.pkg.dev
    _AR_PROJECT_ID: $PROJECT_ID
    _AR_REPOSITORY: cloud-run-source-deploy
    _DEPLOY_REGION: asia-northeast1
    _PLATFORM: managed
    _SERVICE_NAME: pptx-planner
    _TRIGGER_ID: $TRIGGER_ID
  tags:
  - gcp-cloud-build-deploy-cloud-run
  - gcp-cloud-build-deploy-cloud-run-managed
  - pptx-planner
description: Build and deploy to Cloud Run service pptx-planner on push to "^master$"
github:
  name: pptx-planner
  owner: DuyquanDuc
  push:
    branch: ^master$
includeBuildLogs: INCLUDE_BUILD_LOGS_WITH_STATUS
serviceAccount: projects/$PROJECT_ID/serviceAccounts/<EMAIL>
substitutions:
  _AR_HOSTNAME: asia-northeast1-docker.pkg.dev
  _AR_PROJECT_ID: $PROJECT_ID
  _AR_REPOSITORY: cloud-run-source-deploy
  _DEPLOY_REGION: asia-northeast1
  _PLATFORM: managed
  _SERVICE_NAME: pptx-planner
  _TRIGGER_ID: $TRIGGER_ID
tags:
- gcp-cloud-build-deploy-cloud-run
- gcp-cloud-build-deploy-cloud-run-managed
- pptx-planner
EOF

# Update the trigger
echo "📝 Updating trigger configuration..."
gcloud builds triggers import --source=trigger-config.yaml --project=$PROJECT_ID --region=global

# Clean up
rm trigger-config.yaml

echo "✅ Cloud Build trigger updated successfully!"
echo ""
echo "🚀 Next steps:"
echo "1. Push your code changes to the master branch of DuyquanDuc/pptx-planner"
echo "2. The trigger will automatically build and deploy with the new environment variables"
echo "3. Monitor the build: gcloud builds list --limit=5"
echo ""
echo "🔍 The key change: Added --set-env-vars to the deploy step"
echo "   This will set: GCS_BUCKET_NAME, FIREBASE_PROJECT_ID, GOOGLE_CLOUD_PROJECT"
