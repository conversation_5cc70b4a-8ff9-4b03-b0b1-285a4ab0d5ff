PPTXGENJS COMPLETE API REFERENCE - OFFICIAL DEMO VERIFIED
For use in HTML-to-PowerPoint conversion prompts

🎯 **CRITICAL: ONLY USE ELEMENTS LISTED IN THIS REFERENCE**
📚 **SOURCE: Official PptxGenJS demo files - 100% verified working**

## ✅ COMPLETE VALID SHAPES LIST (FROM OFFICIAL DEMO)

**CORE SHAPES - CONFIRMED WORKING:**
```javascript
pptx.shapes.RECTANGLE         // Rectangle/square
pptx.shapes.OVAL             // Circle/ellipse
pptx.shapes.LINE             // Straight line (with arrow options)
pptx.shapes.ROUNDED_RECTANGLE // Rounded corners rectangle
pptx.shapes.ARC              // Arc/curved line with angleRange
pptx.shapes.RIGHT_TRIANGLE   // Right triangle (with flipH option)
pptx.shapes.CUSTOM_GEOMETRY  // Custom shapes with points array
```

**SHAPE PROPERTIES & OPTIONS:**
```javascript
// Rectangle with rotation
pptx.shapes.RECTANGLE, {
  x: 1, y: 1, w: 2, h: 1,
  fill: { color: "FF0000" },
  rotate: 45,  // Rotation in degrees
  line: { color: "000000", width: 2 }
}

// Oval with transparency
pptx.shapes.OVAL, {
  x: 1, y: 1, w: 2, h: 1,
  fill: { color: "00FF00", transparency: 50 },
  rotate: 90
}

// Line with arrows
pptx.shapes.LINE, {
  x: 1, y: 1, w: 4, h: 0,
  line: {
    color: "0000FF",
    width: 3,
    dashType: "dash",           // solid, dash, dot, lgDash, dashDot
    beginArrowType: "triangle", // triangle, diamond, oval, arrow
    endArrowType: "arrow"
  }
}

// Rounded rectangle
pptx.shapes.ROUNDED_RECTANGLE, {
  x: 1, y: 1, w: 2, h: 1,
  rectRadius: 0.5,  // Corner radius
  fill: { color: "FFFF00" },
  line: { color: "000000", width: 1, dashType: "dash" }
}

// Arc with angle range
pptx.shapes.ARC, {
  x: 1, y: 1, w: 2, h: 2,
  fill: { color: "FF00FF" },
  angleRange: [45, 315],  // Start and end angles
  line: { color: "000000", width: 1 }
}

// Right triangle with flip
pptx.shapes.RIGHT_TRIANGLE, {
  x: 1, y: 1, w: 3, h: 2,
  fill: { color: "00FFFF" },
  line: { color: "000000", width: 2 },
  flipH: true,  // Horizontal flip
  shapeName: "MyTriangle"  // Optional name
}
```

**ADVANCED: CUSTOM GEOMETRY:**
```javascript
pptx.shapes.CUSTOM_GEOMETRY, {
  x: 1, y: 1, w: 2, h: 1,
  fill: { color: "FFAA00" },
  line: { color: "000000", width: 1 },
  points: [
    { x: 0.0, y: 0.0 },                    // Start point
    { x: 0.5, y: 1.0 },                    // Line to
    { x: 1.0, y: 0.8 },                    // Line to
    { x: 1.5, y: 1.0 },                    // Line to
    { x: 2.0, y: 0.0 },                    // Line to
    { x: 0.0, y: 0.0, curve: { type: "quadratic", x1: 1.0, y1: 0.5 } }, // Curve
    { close: true }                        // Close path
  ]
}
```

**❌ INVALID SHAPES (CONFIRMED NOT TO EXIST):**
```javascript
// These DO NOT exist in PptxGenJS - DO NOT USE:
pptx.shapes.ACTION_BUTTON_HOME    // ❌ Not real
pptx.shapes.ACTION_BUTTON_HELP    // ❌ Not real
pptx.shapes.SPEECH_BUBBLE         // ❌ Not real
pptx.shapes.GEAR                  // ❌ Not real
pptx.shapes.SHIELD                // ❌ Not real
pptx.shapes.CLOUD                 // ❌ Not real
pptx.shapes.CIRCLE                // ❌ Use OVAL instead
pptx.shapes.TRIANGLE              // ❌ Use RIGHT_TRIANGLE instead
pptx.shapes.DIAMOND               // ❌ Not in official demo
pptx.shapes.PENTAGON              // ❌ Not in official demo
pptx.shapes.HEXAGON               // ❌ Not in official demo
```

## ✅ VALID CHART TYPES (FROM OFFICIAL DEMOS)

**CORE CHART TYPES - CONFIRMED WORKING:**
```javascript
pptx.charts.BAR              // Bar chart (horizontal/vertical)
pptx.charts.BAR3D            // 3D bar chart
pptx.charts.LINE             // Line chart
pptx.charts.AREA             // Area chart
pptx.charts.PIE              // Pie chart
pptx.charts.DOUGHNUT         // Doughnut chart
pptx.charts.SCATTER          // Scatter plot
pptx.charts.BUBBLE           // Bubble chart
pptx.charts.BUBBLE3D         // 3D bubble chart
pptx.charts.RADAR            // Radar chart
```

**IMPORTANT: Use `pptx.charts.CHARTTYPE` NOT `pptx.ChartType.charttype`**

**CHART USAGE EXAMPLES (FROM OFFICIAL DEMOS):**
```javascript
// BAR CHART - Basic
const chartData = [
  {
    name: "Region 1",
    labels: ["Q1", "Q2", "Q3", "Q4"],
    values: [26, 53, 80, 75]
  },
  {
    name: "Region 2",
    labels: ["Q1", "Q2", "Q3", "Q4"],
    values: [43.5, 70.3, 90.01, 80.05]
  }
];

slide.addChart(pptx.charts.BAR, chartData, {
  x: 0.5, y: 0.6, w: 6.0, h: 3.0,
  barDir: "col",                    // "col" (vertical) | "bar" (horizontal)
  barGrouping: "clustered",         // "clustered" | "stacked" | "percentStacked"
  chartColors: ["0088CC", "99FFCC"],
  showTitle: true,
  title: "Sales Data",
  showLegend: true,
  legendPos: "b"                    // "t"|"b"|"l"|"r"
});

// PIE CHART - Advanced
slide.addChart(pptx.charts.PIE, chartData, {
  x: 0.5, y: 0.6, w: 4.0, h: 3.2,
  chartColors: ["FF0000", "00FF00", "0000FF"],
  dataBorder: { pt: 2, color: "F1F1F1" },
  showLegend: true,
  legendPos: "b",
  showPercent: true,
  showValue: true,
  dataLabelColor: "FFFFFF",
  dataLabelFontSize: 14,
  firstSliceAng: 90               // Rotation angle
});

// LINE CHART - With symbols
slide.addChart(pptx.charts.LINE, chartData, {
  x: 0.5, y: 0.6, w: 6.0, h: 3.0,
  lineSize: 4,
  lineSmooth: true,
  lineDataSymbol: "circle",       // "circle"|"dash"|"diamond"|"dot"|"none"|"square"|"triangle"
  lineDataSymbolSize: 10,
  chartColors: ["0088CC"]
});

// 3D BAR CHART
slide.addChart(pptx.charts.BAR3D, chartData, {
  x: 0.5, y: 0.6, w: 6.0, h: 3.0,
  barDir: "col",
  bar3DShape: "cylinder",         // "cylinder"|"pyramid"|"coneToMax"
  v3DRotX: 20,                    // 3D rotation X
  v3DRotY: 10,                    // 3D rotation Y
  v3DRAngAx: false               // Right angle axes
});
```

## ✅ VALID TABLE FEATURES (FROM OFFICIAL DEMOS)

**BASIC TABLE CREATION:**
```javascript
// Simple table
const rows = [
  ["Header 1", "Header 2", "Header 3"],
  ["Data 1", "Data 2", "Data 3"],
  ["Data 4", "Data 5", "Data 6"]
];

slide.addTable(rows, {
  x: 0.5, y: 1.0, w: 8.5, h: 3.0,
  fontSize: 12,
  border: { pt: 1, color: "666666" }
});
```

**ADVANCED TABLE WITH CELL FORMATTING:**
```javascript
const advancedRows = [
  [
    { text: "Top Lft", options: { valign: "top", align: "left", fontFace: "Arial" } },
    { text: "Top Ctr", options: { valign: "top", align: "center", fontFace: "Courier" } },
    { text: "Top Rgt", options: { valign: "top", align: "right", fontFace: "Verdana" } }
  ],
  [
    { text: "white", options: { fill: { color: "6699CC" }, color: "FFFFFF" } },
    { text: "yellow", options: { fill: { color: "99AACC" }, color: "FFFFAA" } },
    { text: "hyperlink", options: { fill: { color: "AACCFF" }, hyperlink: { slide: 2 } } }
  ],
  [
    { text: "Bold", options: { fill: { color: "003366" }, bold: true } },
    { text: "Underline", options: { fill: { color: "336699" }, underline: { style: "sng" } } },
    { text: "0.15 margin", options: { fill: { color: "6699CC" }, margin: 0.15 } }
  ]
];

slide.addTable(advancedRows, {
  x: 0.5, y: 1.1, w: 8.5, h: 2.5,
  rowH: 0.75,                     // Row height
  colW: [2.0, 3.0, 3.5],         // Column widths
  fill: { color: "F7F7F7" },     // Default cell fill
  fontSize: 16,
  color: "FFFFFF",
  valign: "center",
  align: "center",
  border: { pt: 5, color: "FFFFFF" }
});
```

**ROWSPAN/COLSPAN TABLES:**
```javascript
const spanRows = [
  [
    { text: "A1\\nA2", options: { rowspan: 2, fill: { color: "99FFCC" } } },
    { text: "B1" },
    { text: "C1 -> D1", options: { colspan: 2, fill: { color: "99FFCC" } } },
    { text: "E1" }
  ],
  ["B2", "C2", "D2", "E2"],
  ["A3", "B3", "C3", "D3", "E3"]
];

slide.addTable(spanRows, {
  x: 0.67, y: 1.1, w: "90%", h: 2,
  fill: { color: "F9F9F9" },
  color: "3D3D3D",
  fontSize: 16,
  border: { pt: 4, color: "FFFFFF" },
  align: "center",
  valign: "middle"
});
```

**AUTO-PAGING TABLES:**
```javascript
// Large dataset that spans multiple slides
slide.addTable(largeDataArray, {
  x: 0.5, y: 0.5,
  colW: [0.75, 1.75, 10],
  margin: 0.05,
  border: { color: "CFCFCF" },
  autoPage: true,                    // Enable auto-paging
  autoPageRepeatHeader: true,        // Repeat headers on new slides
  autoPageHeaderRows: 1,             // Number of header rows to repeat
  autoPageSlideStartY: 0.6          // Y position for continuation slides
});
```

**WORD-LEVEL FORMATTING IN CELLS:**
```javascript
const cellWithFormatting = [
  [
    {
      text: [
        { text: "Red ", options: { color: "FF0000" } },
        { text: "Green ", options: { color: "00FF00" } },
        { text: "Blue", options: { color: "0000FF" } }
      ]
    }
  ]
];

slide.addTable(cellWithFormatting, {
  x: 0.6, y: 1.25, w: 12, h: 3,
  fontSize: 24,
  border: { pt: 1 },
  fill: { color: "F1F1F1" }
});
```

## ✅ VALID IMAGE FEATURES

**IMAGE USAGE:**
```javascript
// Local image
slide.addImage({ 
  path: "images/logo.png",
  x: 1, y: 1, w: 2, h: 1
});

// Base64 encoded image (preferred for reliability)
slide.addImage({ 
  data: "image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  x: 1, y: 1, w: 2, h: 1
});

// Image with properties
slide.addImage({ 
  path: "images/chart.png",
  x: 1, y: 1, w: 4, h: 3,
  rotate: 45,
  transparency: 50,
  rounding: true  // Makes image circular
});
```

**SUPPORTED IMAGE FORMATS:**
- PNG (recommended)
- JPG/JPEG
- GIF (including animated)
- SVG (newer PowerPoint versions only)

## ✅ VALID TEXT FEATURES (FROM OFFICIAL DEMOS)

**BASIC TEXT FORMATTING:**
```javascript
slide.addText("Sample Text", {
  x: 1, y: 1, w: 6, h: 1,
  fontSize: 18,
  fontFace: "Arial",
  color: "FF0000",
  bold: true,
  italic: true,
  underline: true,
  align: "center",                    // "left"|"center"|"right"
  valign: "middle",                   // "top"|"middle"|"bottom"
  margin: [0.1, 0.1, 0.1, 0.1],     // [top, right, bottom, left]
  lineSpacing: 1.2,
  charSpacing: 16,                    // Character spacing
  fill: { color: "F0F0F0" },         // Background fill
  outline: { color: "000000", size: 1 },
  isTextBox: true                     // Creates actual textbox shape
});
```

**WORD-LEVEL FORMATTING:**
```javascript
const textArray = [
  { text: "Courier New ", options: { fontSize: 36, fontFace: "Courier New", color: "99ABCC" } },
  { text: "36", options: { fontSize: 36, fontFace: "Courier New", color: "FF0000", breakLine: true } },
  { text: "Arial ", options: { fontSize: 48, fontFace: "Arial", color: "00FF00" } },
  { text: "48", options: { fontSize: 48, fontFace: "Courier New", color: "0000FF", breakLine: true } },
  { text: "Strikethrough", options: { fontSize: 36, color: "FF00FF", strike: true, breakLine: true } },
  { text: "Underline", options: { fontSize: 36, color: "00FFFF", underline: true, breakLine: true } },
  { text: "highlighted", options: { fontSize: 36, color: "000000", highlight: "FFFF00" } }
];

slide.addText(textArray, {
  x: 0.5, y: 1.0, w: 5.75, h: 6.0,
  margin: 5,
  fill: { color: "232323" }
});
```

**BULLETS AND NUMBERING:**
```javascript
// Basic bullets
slide.addText("Line 1\\nLine 2\\nLine 3", {
  x: 1, y: 1, w: 6, h: 2,
  bullet: true,
  fontSize: 16
});

// Advanced bullets with indent levels
const bulletText = [
  { text: "Root-Level", options: { bullet: true, indentLevel: 0, color: "FF0000" } },
  { text: "Indent-Level 1", options: { bullet: true, indentLevel: 1, color: "00FF00" } },
  { text: "Indent-Level 2", options: { bullet: true, indentLevel: 2, color: "0000FF" } },
  { text: "Indent-Level 3", options: { bullet: true, indentLevel: 3, color: "FF00FF" } }
];

slide.addText(bulletText, {
  x: 0.5, y: 1.0, w: 5.75, h: 2.4,
  fill: { color: "232323" }
});

// Numbered bullets
slide.addText("Item 1\\nItem 2\\nItem 3", {
  x: 1, y: 3, w: 6, h: 2,
  bullet: { type: "number", style: "arabicPeriod" },
  fontSize: 16
});

// Custom bullet characters
const customBullets = [
  { text: "Custom bullet 1", options: { bullet: { code: "25BA" }, color: "FF0000" } },
  { text: "Custom bullet 2", options: { bullet: { code: "25D1" }, color: "00FF00" } },
  { text: "Custom bullet 3", options: { bullet: { code: "25CC" }, color: "0000FF" } }
];

slide.addText(customBullets, {
  x: 1, y: 5, w: 6, h: 2,
  fontFace: "Arial",
  fill: { color: "F1F1F1" }
});
```

**HYPERLINKS:**
```javascript
const hyperlinkText = [
  { text: "Link with Tooltip", options: {
    hyperlink: { url: "https://github.com/gitbrent/pptxgenjs", tooltip: "Visit Homepage" }
  }},
  { text: "Link without Tooltip", options: {
    hyperlink: { url: "https://github.com/gitbrent" }
  }},
  { text: "Link with custom color", options: {
    hyperlink: { url: "https://github.com/gitbrent" },
    color: "EE40EE"
  }},
  { text: "Link to Slide #5", options: {
    hyperlink: { slide: 5 }
  }}
];

slide.addText(hyperlinkText, {
  x: 0.5, y: 1.0, w: 12, h: 1,
  fontSize: 14,
  align: "center"
});
```

**TEXT EFFECTS:**
```javascript
// Text Shadow
slide.addText("Text with Shadow", {
  x: 1, y: 1, w: 6, h: 1,
  fontSize: 32,
  color: "0088cc",
  shadow: {
    type: "outer",
    color: "696969",
    blur: 3,
    offset: 10,
    angle: 45,
    opacity: 0.6
  }
});

// Text Outline
slide.addText("Text with Outline", {
  x: 1, y: 2, w: 6, h: 1,
  fontSize: 72,
  bold: true,
  color: "FF0000",
  outline: { size: 2, color: "0000FF" }
});

// Text Glow
slide.addText("Text with Glow", {
  x: 1, y: 3, w: 6, h: 1,
  fontSize: 72,
  color: "FF0000",
  glow: { size: 10, opacity: 0.25, color: "00FF00" }
});
```

**TAB STOPS:**
```javascript
slide.addText("text...\\tTab1\\tTab2\\tTab3", {
  x: 0.5, y: 2.5, w: 12.3, h: 0.6,
  tabStops: [
    { position: 1 },
    { position: 3 },
    { position: 7 }
  ],
  fill: { color: "F1F1F1" }
});
```

**TEXT FIT OPTIONS:**
```javascript
// Shrink text to fit
slide.addText(longText, {
  x: 0.5, y: 1.3, w: 4, h: 4,
  fontSize: 12,
  fit: "shrink"                    // "none"|"shrink"|"resize"
});
```

**TEXT WITH SHAPES (FROM OFFICIAL DEMO):**
```javascript
// Text in rectangle
slide.addText("RECTANGLE", {
  shape: pptx.shapes.RECTANGLE,
  x: 0.5, y: 0.8, w: 1.5, h: 3.0,
  fill: { color: "FF0000" },
  align: "center",
  fontSize: 14
});

// Text in oval with transparency
slide.addText("OVAL (transparency:50)", {
  shape: pptx.shapes.OVAL,
  x: 2.2, y: 0.8, w: 3.0, h: 1.5,
  fill: { type: "solid", color: "00FF00", transparency: 50 },
  align: "center",
  fontSize: 14
});

// Text in rotated rectangle
slide.addText("RECTANGLE (rotate:45)", {
  shape: pptx.shapes.RECTANGLE,
  x: 5.7, y: 0.8, w: 1.5, h: 3.0,
  fill: { color: "0000FF" },
  rotate: 45,
  align: "center",
  fontSize: 14
});

// Text in rounded rectangle with dashed border
slide.addText("ROUNDED-RECTANGLE\\ndashType:dash\\nrectRadius:1", {
  shape: pptx.shapes.ROUNDED_RECTANGLE,
  x: 10, y: 0.8, w: 3.0, h: 1.5,
  fill: { color: "FFFF00" },
  align: "center",
  fontSize: 14,
  line: { color: "151515", size: 1, dashType: "dash" },
  rectRadius: 1
});

// Text in arc
slide.addText("ARC", {
  shape: pptx.shapes.ARC,
  x: 10.75, y: 2.45, w: 1.5, h: 1.45,
  fill: { color: "FF00FF" },
  angleRange: [45, 315],
  line: { color: "151515", width: 1 },
  fontSize: 14
});

// Text on line (positioned text)
slide.addText("LINE size=1", {
  shape: pptx.shapes.LINE,
  align: "center",
  x: 4.15, y: 4.4, w: 5, h: 0,
  line: { color: "00FFFF", width: 1, dashType: "lgDash" }
});

// Text in triangle with hyperlink
slide.addText("HYPERLINK-SHAPE", {
  shape: pptx.shapes.RIGHT_TRIANGLE,
  align: "center",
  x: 7.0, y: 4.3, w: 6, h: 3,
  fill: { color: "FFAA00" },
  line: { color: "696969", width: 2 },
  flipH: true,
  hyperlink: { url: "https://github.com/gitbrent/pptxgenjs", tooltip: "Visit Homepage" }
});
```

## ✅ POSITIONING & SIZING RULES

**SLIDE DIMENSIONS:**
- Standard slide: 10" × 5.625" (16:9 aspect ratio)
- Safe content area: x: 0.3-8.5, y: 0.3-4.8

**POSITIONING UNITS:**
```javascript
// Inches (recommended)
{ x: 1.0, y: 2.0, w: 4.0, h: 1.5 }

// Percentages
{ x: "10%", y: "20%", w: "40%", h: "15%" }
```

**ULTRA-SAFE POSITIONING:**
```javascript
// Two-column layout (guaranteed safe)
const LEFT_COL = { x: 0.3, w: 3.8 };   // Ends at 4.1
const RIGHT_COL = { x: 4.3, w: 3.8 };  // Ends at 8.1

// Vertical positioning
const TITLE_Y = 0.3;
const CONTENT_START_Y = 1.0;
const MAX_CONTENT_Y = 4.8;  // Never exceed this
```

## ✅ COLOR FORMATS

**VALID COLOR FORMATS:**
```javascript
// Hex colors (without #)
color: "FF0000"     // Red
color: "00FF00"     // Green  
color: "0000FF"     // Blue
color: "FFFFFF"     // White
color: "000000"     // Black

// Named colors (limited support)
color: "red"
color: "blue"
color: "green"
```

## ✅ BORDER & LINE STYLES

**BORDER PROPERTIES:**
```javascript
border: { 
  type: "solid",      // solid, dash, dot
  pt: 1,              // Point size
  color: "000000"     // Hex color
}

line: { 
  color: "FF0000", 
  width: 2,
  dashType: "solid"   // solid, dash, dot, lgDash
}
```

## ❌ COMMON MISTAKES TO AVOID

**INVALID SYNTAX:**
```javascript
// ❌ Wrong - causes errors
pptx.shapes.CIRCLE           // Use OVAL instead
pptx.shapes.ACTION_BUTTON_*  // Action buttons don't exist
slide.addShape("rectangle")  // Must use pptx.shapes.RECTANGLE

// ❌ Wrong positioning
{ x: 12, y: 6 }             // Exceeds slide boundaries
{ x: -1, y: -1 }            // Negative positions

// ❌ Wrong colors
color: "#FF0000"            // Remove the #
color: "rgb(255,0,0)"       // Use hex instead
```

**SAFE ALTERNATIVES:**
```javascript
// ✅ Correct
pptx.shapes.OVAL             // Instead of CIRCLE
pptx.shapes.RECTANGLE        // For action buttons
{ x: 0.3, y: 0.3, w: 8.2, h: 4.5 }  // Safe positioning
color: "FF0000"              // Proper hex format
```

## 🎯 PROMPT USAGE GUIDELINES

**WHEN WRITING PROMPTS:**
1. Only reference shapes/features listed in this guide
2. Always use ultra-safe positioning (x: 0.3-8.5, y: 0.3-4.8)
3. Prefer basic shapes (RECTANGLE, OVAL, LINE) over complex ones
4. Use proper hex color format (no # symbol)
5. Include overflow prevention checks
6. Test positioning calculations before generating

**EXAMPLE SAFE PROMPT SNIPPET:**
```javascript
// Ultra-safe shape usage
slide.addShape(pptx.shapes.RECTANGLE, {
  x: 0.5, y: 1.0, w: 2.0, h: 1.0,
  fill: { color: "64ffda" },
  line: { color: "1a3a6e", width: 1 }
});

// Ultra-safe text positioning  
slide.addText("Safe Content", {
  x: 0.3, y: 1.0, w: 8.2, h: 0.5,
  fontSize: 16, color: "000000"
});
```

This reference ensures all generated code uses only valid PptxGenJS features!
