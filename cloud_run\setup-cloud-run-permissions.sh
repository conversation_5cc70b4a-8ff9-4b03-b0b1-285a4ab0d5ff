#!/bin/bash

# Setup Cloud Run Service Account Permissions
# This assigns the necessary roles to the default Cloud Run service account

PROJECT_ID="gen-lang-client-**********"
PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")
CLOUD_RUN_SA="${PROJECT_NUMBER}-<EMAIL>"

echo "🔧 Setting up Cloud Run service account permissions..."
echo "Project ID: $PROJECT_ID"
echo "Project Number: $PROJECT_NUMBER"
echo "Cloud Run Service Account: $CLOUD_RUN_SA"

# Set the project
gcloud config set project $PROJECT_ID

# Assign Firebase Admin role to Cloud Run service account
echo "🔥 Assigning Firebase Admin role..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$CLOUD_RUN_SA" \
    --role="roles/firebase.admin"

# Assign Storage Object Admin role to Cloud Run service account
echo "☁️ Assigning Storage Object Admin role..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$CLOUD_RUN_SA" \
    --role="roles/storage.objectAdmin"

# Assign Storage Object Creator role for signed URLs
echo "🔗 Assigning Storage Object Creator role..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$CLOUD_RUN_SA" \
    --role="roles/storage.objectCreator"

# Optional: Assign specific bucket permissions (more secure)
echo "🪣 Setting bucket-specific permissions..."
gsutil iam ch serviceAccount:$CLOUD_RUN_SA:objectAdmin gs://pptx-planner-storage

echo "✅ Cloud Run service account permissions setup complete!"
echo ""
echo "📋 The Cloud Run service will now be able to:"
echo "  - Verify Firebase authentication tokens"
echo "  - Read/write files to Google Cloud Storage"
echo "  - Generate signed URLs for file downloads"
echo ""
echo "🚀 You can now deploy your app with:"
echo "  ./deploy-cloud-run.sh"
