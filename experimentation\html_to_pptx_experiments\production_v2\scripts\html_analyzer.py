#!/usr/bin/env python3
"""
HTML Analyzer for HTML-to-PowerPoint pipeline (production_v2)
- Extracts colors (hex, rgb/rgba, hsl/hsla, CSS variables)
- Detects visual elements (SVG, Canvas, FontAwesome, emojis) with quantitative complexity metrics
- Captures simple layout/styling cues (grid/flex/columns)
- Parses style blocks for class color definitions

Designed to be dependency-light (regex-based) to avoid new installs.
"""
from __future__ import annotations

import re
from typing import Dict, List, Any, Tuple

# ---- Color helpers ---------------------------------------------------------

def _normalize_hex(h: str) -> str:
    h = h.strip().lstrip('#')
    if len(h) == 3:
        h = ''.join(ch * 2 for ch in h)
    return h.upper()


def _clamp(v: int, lo: int = 0, hi: int = 255) -> int:
    return max(lo, min(hi, int(round(v))))


def _rgb_to_hex(rgb: Tuple[int, int, int]) -> str:
    r, g, b = (_clamp(rgb[0]), _clamp(rgb[1]), _clamp(rgb[2]))
    return f"{r:02X}{g:02X}{b:02X}"


def _parse_rgb(s: str) -> Tuple[int, int, int]:
    # Accept rgb(255, 0, 128) or rgba(255,0,128,0.5)
    nums = [n.strip() for n in re.split(r'[(),]', s) if n.strip()]
    # nums like ['rgb', '255', '0', '128']
    vals = [v for v in nums if v.isdigit()]
    if len(vals) >= 3:
        return int(vals[0]), int(vals[1]), int(vals[2])
    # Percent form rgb(100%, 0%, 50%)
    perc = re.findall(r'(\d+)%', s)
    if len(perc) >= 3:
        return int(int(perc[0]) * 2.55), int(int(perc[1]) * 2.55), int(int(perc[2]) * 2.55)
    return 0, 0, 0


def _hsl_to_hex(h: float, s: float, l: float) -> str:
    # h in [0..360], s,l in [0..100]
    h = h % 360
    s = max(0, min(100, s)) / 100.0
    l = max(0, min(100, l)) / 100.0
    c = (1 - abs(2 * l - 1)) * s
    x = c * (1 - abs((h / 60.0) % 2 - 1))
    m = l - c / 2
    r = g = b = 0
    if 0 <= h < 60:
        r, g, b = c, x, 0
    elif 60 <= h < 120:
        r, g, b = x, c, 0
    elif 120 <= h < 180:
        r, g, b = 0, c, x
    elif 180 <= h < 240:
        r, g, b = 0, x, c
    elif 240 <= h < 300:
        r, g, b = x, 0, c
    else:
        r, g, b = c, 0, x
    return _rgb_to_hex((int((r + m) * 255), int((g + m) * 255), int((b + m) * 255)))


def _parse_hsl(s: str) -> str:
    # hsl(330, 100%, 71%) or hsla(...)
    m = re.search(r'hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)%\s*,\s*(\d+(?:\.\d+)?)%', s)
    if not m:
        return '000000'
    h, ss, ll = float(m.group(1)), float(m.group(2)), float(m.group(3))
    return _hsl_to_hex(h, ss, ll)


# ---- Extraction ------------------------------------------------------------

def extract_colors(html: str) -> Dict[str, Any]:
    # Hex colors
    hexes = [
        _normalize_hex(h) for h in re.findall(r'#[A-Fa-f0-9]{3,6}', html)
    ]

    # rgb/rgba
    rgb_funcs = re.findall(r'rgba?\([^\)]*\)', html)
    rgb_hexes = [_rgb_to_hex(_parse_rgb(s)) for s in rgb_funcs]

    # hsl/hsla
    hsl_funcs = re.findall(r'hsla?\([^\)]*\)', html)
    hsl_hexes = [_parse_hsl(s) for s in hsl_funcs]

    # CSS variables definitions: --var: value;
    css_vars = dict(re.findall(r'--([a-zA-Z0-9_-]+)\s*:\s*([^;}{]+);', html))
    # Normalize var values if they look like colors
    normalized_vars: Dict[str, str] = {}
    for k, v in css_vars.items():
        v = v.strip()
        if v.startswith('#'):
            normalized_vars[k] = _normalize_hex(v)
        elif v.startswith('rgb'):
            normalized_vars[k] = _rgb_to_hex(_parse_rgb(v))
        elif v.startswith('hsl'):
            normalized_vars[k] = _parse_hsl(v)
        else:
            normalized_vars[k] = v

    # var() usages
    var_usages = re.findall(r'var\(--([a-zA-Z0-9_-]+)\)', html)

    palette = list({c for c in hexes + rgb_hexes + hsl_hexes if re.match(r'^[A-F0-9]{6}$', c)})

    return {
        'hex': list(set(hexes)),
        'rgb_funcs': rgb_funcs,
        'hsl_funcs': hsl_funcs,
        'css_variables': normalized_vars,
        'var_usages': list(set(var_usages)),
        'palette_hex': palette,
    }


def extract_class_styles(html: str) -> Dict[str, Dict[str, str]]:
    """Very light CSS class color/background-color parser from <style> blocks."""
    styles: Dict[str, Dict[str, str]] = {}
    for block in re.findall(r'<style[^>]*>(.*?)</style>', html, flags=re.DOTALL | re.IGNORECASE):
        # Find .class { ... }
        for sel, body in re.findall(r'(\.[a-zA-Z0-9_-]+)\s*\{([^}]*)\}', block):
            color = None
            bg = None
            m = re.search(r'color\s*:\s*([^;]+);', body)
            if m:
                color = m.group(1).strip()
            m = re.search(r'background(-color)?\s*:\s*([^;]+);', body)
            if m:
                bg = m.group(2).strip()

            def norm(col: str | None) -> str | None:
                if not col:
                    return None
                c = col.strip()
                if c.startswith('#'):
                    return _normalize_hex(c)
                if c.startswith('rgb'):
                    return _rgb_to_hex(_parse_rgb(c))
                if c.startswith('hsl'):
                    return _parse_hsl(c)
                return c

            rec: Dict[str, str] = {}
            nc, nb = norm(color), norm(bg)
            if nc:
                rec['color'] = nc
            if nb:
                rec['backgroundColor'] = nb
            if rec:
                styles[sel] = rec
    return styles


def detect_visual_elements(html: str) -> Dict[str, Any]:
    """Detect visual elements and compute a visual complexity score."""
    lower = html.lower()
    has_svg = bool(re.search(r'<svg[\s>]', html, flags=re.IGNORECASE))
    has_canvas = '<canvas' in lower

    # Icon libraries
    fa_classes = re.findall(r'class\s*=\s*"[^"]*(fa[-\s][a-z0-9-]+)[^"]*"', html, flags=re.IGNORECASE)
    icon_classes = re.findall(r'class\s*=\s*"[^"]*(icon[-\s][a-z0-9-]+)[^"]*"', html, flags=re.IGNORECASE)

    # Emoji detection via broad ranges
    try:
        emojis_found = re.findall(r"[\U0001F300-\U0001FAFF\U00002600-\U000027BF]", html)
    except re.error:
        emojis_found = [ch for ch in html if 0x1F300 <= ord(ch) <= 0x1FAFF]

    # Count elements for density
    svg_elements = len(re.findall(r'<svg[\s>]', html, flags=re.IGNORECASE))
    svg_paths = len(re.findall(r'<path\s', html, flags=re.IGNORECASE))
    canvas_elements = lower.count('<canvas')
    icon_count = len(fa_classes) + len(icon_classes)
    emoji_count = len(emojis_found)

    # Visual complexity score (tunable weights)
    visual_score = (svg_elements * 3) + (svg_paths * 2) + (canvas_elements * 4) + (icon_count * 2) + (emoji_count * 1)
    complex_visuals = visual_score >= 5 or has_svg or has_canvas

    return {
        'has_svg': has_svg,
        'has_canvas': has_canvas,
        'svg_elements': svg_elements,
        'svg_paths': svg_paths,
        'canvas_elements': canvas_elements,
        'fontawesome': list(set(fa_classes)),
        'icon_classes': list(set(icon_classes)),
        'emojis': list(set(emojis_found)),
        'visual_complexity_score': visual_score,
        'complex_visuals': complex_visuals,
    }


def analyze_layout(html: str) -> Dict[str, Any]:
    lower = html.lower()
    grid = 'display:grid' in lower or 'grid-' in lower or ' class="grid' in lower
    flex = 'display:flex' in lower or ' class="flex' in lower or 'flex-' in lower
    columns = lower.count('col-') + (1 if 'two-column' in lower or 'left-column' in lower else 0) + (1 if 'right-column' in lower else 0)
    return {
        'grid': grid,
        'flex': flex,
        'columns_hint': columns if columns > 0 else (2 if ('left-column' in lower and 'right-column' in lower) else 1)
    }


def analyze_html(html_content: str) -> Dict[str, Any]:
    colors = extract_colors(html_content)
    class_styles = extract_class_styles(html_content)
    visuals = detect_visual_elements(html_content)
    layout = analyze_layout(html_content)

    # Distinct colors from inline and class styles
    distinct_colors = set(colors.get('palette_hex', []))
    for v in class_styles.values():
        for k in ('color', 'backgroundColor'):
            if k in v and re.match(r'^[A-F0-9]{6}$', v[k]):
                distinct_colors.add(v[k])

    # Color diversity metric
    color_diversity = len(distinct_colors)

    # Complexity indicators
    gradient_count = len(re.findall(r'linear-gradient|radial-gradient', html_content, flags=re.IGNORECASE))
    inline_color_styles = len(re.findall(r'style\s*=\s*"[^"]*(color|background-color)\s*:', html_content, flags=re.IGNORECASE))
    class_color_defs = sum(1 for v in class_styles.values() if 'color' in v or 'backgroundColor' in v)

    # Composite color complexity score
    color_complexity_score = color_diversity + gradient_count * 2 + inline_color_styles + class_color_defs

    # Final routing flags
    color_rich = color_diversity >= 4 or color_complexity_score >= 8
    visual_dense = visuals.get('visual_complexity_score', 0) >= 5

    return {
        'colors': colors,
        'class_styles': class_styles,
        'visuals': visuals,
        'layout': layout,
        'metrics': {
            'color_diversity': color_diversity,
            'color_complexity_score': color_complexity_score,
            'visual_complexity_score': visuals.get('visual_complexity_score', 0),
            'gradient_count': gradient_count,
            'inline_color_styles': inline_color_styles,
            'class_color_defs': class_color_defs,
        },
        'flags': {
            'color_rich': color_rich,
            'complex_visuals': visual_dense,
        }
    }

