import google.genai as genai
from google.genai import types
from typing import List, Union
from PIL import Image
import random
from google.genai.errors import ServerError
import io
import asyncio # Crucial for running blocking API calls in a thread
from loguru import logger

class Gemini_HTML_Translator_LLM:
    """
    Dedicated Gemini LLM provider for HTML-to-PowerPoint translation.
    
    Key differences from main Gemini provider:
    - NO TOOLS (no image_tavily or other tools)
    - Optimized for pure text generation
    - Focused on HTML translation tasks
    """
    def __init__(self, api_key: str, model: str = 'gemini-1.5-pro', temperature: float = 0,
                  max_retries: int = 2, base_backoff_delay: float = 10.0):
        self.model = model # Renamed to avoid confusion with `self.model` instance
        self.temperature = temperature
        # Directly get the GenerativeModel instance
        self.client = genai.Client(api_key=api_key)
        self.max_retries = max_retries
        self.base_backoff_delay = base_backoff_delay # Initial delay in seconds
        self.max_backoff_delay = 45.0 # Maximum delay between retries in seconds

    async def _api_call_with_retries(self, func, *args, **kwargs):
        """
        Helper method to perform an API call with retries and exponential backoff.
        Catches ServiceUnavailable (503) and ResourceExhausted (429) errors.

        Args:
            func: The callable (e.g., self.client.models.generate_content) to execute.
            *args: Positional arguments for func.
            **kwargs: Keyword arguments for func.
        """
        for attempt in range(self.max_retries + 1): # +1 to include the initial attempt
            try:
                # Execute the blocking call in a separate thread
                response = await asyncio.to_thread(func, *args, **kwargs)
                return response
            
            except ServerError as e:
                if e.code in [503, 429]:
                    if attempt < self.max_retries:
                        delay = min(self.base_backoff_delay * (2 ** attempt), self.max_backoff_delay)
                        jitter = random.uniform(0, delay * 0.1) # Add up to 10% jitter to prevent thundering herd problem
                        sleep_time = delay + jitter
                        logger.error(f"[Error] {e} : Retrying in {sleep_time:.2f} seconds (attempt {attempt + 1}/{self.max_retries}).")
                        await asyncio.sleep(sleep_time)
                    else:
                        logger.error(f"[Error] {e} : Retries exceeded.")
                        raise # Re-raise the last exception if all retries are exhausted
                else:
                    raise # Re-raise non-retryable errors immediately

    async def call(self, query: str):
        """
        Pure text generation call - NO TOOLS for HTML translation tasks
        """
        # Configure generation settings WITHOUT any tools
        config_args = {"temperature": self.temperature}

        # Only add thinking config for models that support it (gemini-2.5-pro)
        if "2.5" in self.model:
            config_args["thinking_config"] = types.ThinkingConfig(thinking_budget=1000)
            logger.debug(f"🧠 Using thinking budget for {self.model}")
        else:
            logger.debug(f"🚫 No thinking budget for {self.model} (not supported)")

        config = genai.types.GenerateContentConfig(**config_args)

        response = await self._api_call_with_retries(
            self.client.models.generate_content,
            model=self.model,
            contents=query,
            config=config
        )

        # Debug: Log response details
        logger.debug(f"🔍 Response type: {type(response)}")
        logger.debug(f"🔍 Response attributes: {dir(response)}")

        # Safe check for response.text
        if hasattr(response, 'text'):
            if response.text is not None:
                logger.debug(f"🔍 Response text length: {len(response.text)}")
            else:
                logger.debug(f"🔍 Response text is None")
        else:
            logger.debug(f"🔍 No text attribute")

        # Check if response has text
        if hasattr(response, 'text') and response.text:
            logger.debug(f"🔍 Response text preview: {response.text[:200]}...")
            return response.text
        else:
            # Try alternative ways to get the response
            logger.warning("⚠️ response.text is empty or None, trying alternatives...")

            # Debug: Check candidates structure
            if hasattr(response, 'candidates'):
                logger.debug(f"🔍 Candidates count: {len(response.candidates) if response.candidates else 0}")
                if response.candidates:
                    candidate = response.candidates[0]
                    logger.debug(f"🔍 Candidate attributes: {dir(candidate)}")

                    # Check finish_reason and safety_ratings
                    if hasattr(candidate, 'finish_reason'):
                        logger.debug(f"🔍 Finish reason: {candidate.finish_reason}")
                    if hasattr(candidate, 'safety_ratings'):
                        logger.debug(f"🔍 Safety ratings: {candidate.safety_ratings}")

                    if hasattr(candidate, 'content') and candidate.content:
                        logger.debug(f"🔍 Content attributes: {dir(candidate.content)}")
                        if hasattr(candidate.content, 'parts') and candidate.content.parts:
                            logger.debug(f"🔍 Parts count: {len(candidate.content.parts)}")
                            part = candidate.content.parts[0]
                            logger.debug(f"🔍 Part attributes: {dir(part)}")

                            if hasattr(part, 'text') and part.text:
                                text_content = part.text
                                logger.debug(f"🔍 Found text in candidates: {len(text_content)} chars")
                                return text_content
                            else:
                                logger.warning("⚠️ Part has no text or text is None")
                        else:
                            logger.warning("⚠️ Content has no parts")
                    else:
                        logger.warning("⚠️ Candidate has no content")
                else:
                    logger.warning("⚠️ No candidates in response")
            else:
                logger.warning("⚠️ Response has no candidates attribute")

            # If all else fails, return empty string to avoid None issues
            logger.error("❌ Could not extract any text from response")
            return ""

    async def call_with_images(self, query: str, images: List[Union[str, Image.Image]]):
        """
        Call with images support (though HTML translator typically won't need this)
        """
        # Process images
        processed_images = []
        for img in images:
            if isinstance(img, str):
                # Assume it's a file path
                processed_images.append(Image.open(img))
            elif isinstance(img, Image.Image):
                processed_images.append(img)
            else:
                raise ValueError(f"Unsupported image type: {type(img)}")

        # Convert PIL Images to bytes
        image_bytes_list = []
        for img in processed_images:
            img_byte_arr = io.BytesIO()
            img.save(img_byte_arr, format='PNG')
            image_bytes_list.append(img_byte_arr.getvalue())

        # Configure generation settings WITHOUT tools
        config_args = {"temperature": self.temperature}

        # Only add thinking config for models that support it (gemini-2.5-pro)
        if "2.5" in self.model:
            config_args["thinking_config"] = types.ThinkingConfig(thinking_budget=128)

        config = genai.types.GenerateContentConfig(**config_args)

        # Prepare content with images
        content_parts = [query]
        for img_bytes in image_bytes_list:
            content_parts.append(types.Part.from_bytes(img_bytes, mime_type="image/png"))

        response = await self._api_call_with_retries(
            self.client.models.generate_content,
            model=self.model,
            contents=content_parts,
            config=config
        )
        
        return response.text

    def get_model_info(self):
        """
        Return information about this LLM provider
        """
        return {
            "provider": "gemini_html_translator",
            "model": self.model,
            "temperature": self.temperature,
            "tools": "none",  # Key difference - no tools
            "purpose": "HTML-to-PowerPoint translation"
        }
