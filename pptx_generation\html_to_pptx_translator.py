"""
HTML to PptxGenJS Translator

This module provides functions to convert HTML slides to PptxGenJS JavaScript code
using LLM-based analysis with enhanced positioning and dynamic color extraction.
"""

import re
import textwrap
import os
import subprocess
import shutil
import time
import tempfile
from datetime import datetime
from typing import List, Dict, Any


# HTML Utility Functions
def extract_text_from_html(html_content: str) -> str:
    """Extract text content from HTML"""
    # Remove HTML tags and get clean text
    clean_text = re.sub('<[^<]+?>', '', html_content)
    # Clean up extra whitespace
    clean_text = re.sub(r'\s+', ' ', clean_text).strip()
    return clean_text


def extract_title_from_html(html_content: str) -> str:
    """Extract title from HTML (h1, h2, or title tag)"""
    # Try to find h1 first
    h1_match = re.search(r'<h1[^>]*>(.*?)</h1>', html_content, re.IGNORECASE | re.DOTALL)
    if h1_match:
        return extract_text_from_html(h1_match.group(1))

    # Try h2
    h2_match = re.search(r'<h2[^>]*>(.*?)</h2>', html_content, re.IGNORECASE | re.DOTALL)
    if h2_match:
        return extract_text_from_html(h2_match.group(1))

    # Try title tag
    title_match = re.search(r'<title[^>]*>(.*?)</title>', html_content, re.IGNORECASE | re.DOTALL)
    if title_match:
        return extract_text_from_html(title_match.group(1))

    return "Slide Title"


def extract_content_from_html(html_content: str) -> str:
    """Extract main content from HTML (p tags, body content)"""
    # Try to find p tags
    p_matches = re.findall(r'<p[^>]*>(.*?)</p>', html_content, re.IGNORECASE | re.DOTALL)
    if p_matches:
        content = ' '.join([extract_text_from_html(p) for p in p_matches])
        return content

    # Fallback: extract all text from body
    body_match = re.search(r'<body[^>]*>(.*?)</body>', html_content, re.IGNORECASE | re.DOTALL)
    if body_match:
        return extract_text_from_html(body_match.group(1))

    # Final fallback: extract all text
    return extract_text_from_html(html_content)


# LLM-based HTML to PptxGenJS Translation Functions


async def llm_html_to_pptxgenjs_single(html_content: str, slide_name: str, llm) -> str:
    """
    Convert a single HTML slide to PptxGenJS code using LLM analysis

    Args:
        html_content: HTML content of the slide
        slide_name: Name/title of the slide for context
        llm: LLM instance for translation

    Returns:
        JavaScript code for creating the slide with PptxGenJS
    """
    prompt = textwrap.dedent(f"""
    You are an expert JavaScript developer specializing in PptxGenJS. Convert this HTML slide to PptxGenJS code.

    STEP 1: ANALYZE THE HTML FIRST
    Before generating code, carefully examine the HTML for:
    - Background colors (from CSS styles, body, containers)
    - Text colors (from CSS classes, inline styles, color properties)
    - Font sizes and hierarchy (h1, h2, h3, p tags and their CSS)
    - Layout structure (grid, flexbox, columns, sections)
    - Content emphasis (bold, italic, color variations)

    STEP 2: EXTRACT DYNAMIC VALUES
    - Background: Look for background-color, background, or theme colors
    - Text colors: Extract from color properties, CSS classes
    - Font sizes: Map HTML hierarchy → h1=32-40px, h2=20-28px, h3=16-20px, p=12-16px
    - Layout: Detect grid patterns, column structures, content sections

    CSS-TO-PPTXGENJS MAPPING GUIDE:
    - background-color: #0D1B2A → slide.background = {{ color: '0D1B2A' }}
    - color: #E0E0E0 → color: 'E0E0E0'
    - font-weight: bold → bold: true
    - font-style: italic → italic: true
    - text-align: center → align: 'center'
    - Remove # from hex colors: #FF6B6B → 'FF6B6B'

    COLOR FORMATTING RULES - CRITICAL:
    - Colors must be simple quoted strings: 'FFFFFF', '2C3E50', 'E0E0E0'
    - NO extra quotes or backslashes: 'FFFFFF\' is WRONG
    - NO hash symbols: '#FFFFFF' is WRONG
    - NO variables: BG_COLOR is WRONG
    - NO backslashes anywhere: \' is FORBIDDEN
    - CORRECT format: slide.background = {{ color: 'FFFFFF' }};

    STRING FORMATTING RULES - CRITICAL:
    - ALL text must be quoted: slide.addText('Title Text', {{ ... }})
    - ALL colors must be quoted: color: 'FFFFFF', color: '2C3E50'
    - ALL text in arrays must be quoted: {{ text: '* Bullet point', ... }}
    - NO unquoted text: addText(Title, {{ is WRONG
    - NO unquoted colors: color: FFFFFF is WRONG
    - For apostrophes in text, escape them: 'we\'ll' not 'we'll'
    - CORRECT: slide.addText('Don\'t use backslashes', {{ color: '2C3E50' }});

    FALLBACK VALUES (use if HTML analysis unclear):
    - Default background: '0D1B2A' (dark blue)
    - Default text color: 'E0E0E0' (light gray)
    - Default accent color: '88CCEE' (light blue)
    - Default title size: 32, subtitle: 20, content: 14

    STEP 3: GENERATE PPTXGENJS CODE
    CRITICAL RULES - FOLLOW EXACTLY:
    1. Generate a COMPLETE function called createPresentation()
    2. ALWAYS start with: const pptx = new PptxGenJS();
    3. ALWAYS end with: return pptx.writeFile({{ fileName: 'presentation.pptx' }});
    4. Use ONLY simple addText() calls - NO complex nested objects
    5. Colors MUST be literal strings extracted from HTML: color: 'E0E0E0' (NOT variables)

    POSITIONING RULES - CRITICAL FOR PROPER LAYOUT:
    6. SLIDE DIMENSIONS: Standard slide is 10 inches wide × 5.625 inches tall
    7. SAFE MARGINS: Keep content between x: 0.5-9.5 and y: 0.5-5.0
    8. TITLE POSITIONING: Main titles at y: 0.5-1.0, subtitles at y: 1.2-1.8
    9. CONTENT SPACING: Leave 0.3-0.5 inches between text blocks vertically
    10. GRID LAYOUTS: For 2-column layouts use x: 0.5,5.25 with w: 4.0 each
    11. TEXT WIDTH: Always specify w: (width) to prevent text overflow
    12. VERTICAL FLOW: Start content at y: 2.0, increment by 0.8-1.0 per section

    HTML to convert:
    {html_content}

    Follow these WORKING EXAMPLES EXACTLY:

    EXAMPLE 1 - Dynamic color extraction from HTML:

    HTML Input: <body style='background-color: #2C3E50'><h1 style='color: #ECF0F1'>AI-Powered Document Search</h1></body>

    ```javascript
    function createPresentation() {{
        const pptx = new PptxGenJS();
        const slide = pptx.addSlide();

        // EXTRACTED: background-color: #2C3E50 → '2C3E50'
        slide.background = {{ color: '2C3E50' }};

        // EXTRACTED: h1 + color: #ECF0F1 → fontSize: 36, color: 'ECF0F1'
        slide.addText('AI-Powered Document Search', {{
            x: 0.5,
            y: 0.8,
            w: 9.0,
            h: 1.2,
            fontSize: 36,
            color: 'ECF0F1',
            bold: true,
            align: 'center'
        }});

        return pptx.writeFile({{ fileName: 'presentation.pptx' }});
    }}
    ```

    EXAMPLE 2 - Grid layout with multiple sections:

    HTML Input: <div class='grid-2x2' style='background: #1A252F'>
                  <h1 style='color: #FFFFFF'>Executive Summary</h1>
                  <section class='problem'><h3 style='color: #E74C3C'>Problem</h3><p>Current search is inefficient</p></section>
                  <section class='solution'><h3 style='color: #2ECC71'>Solution</h3><p>AI-powered system</p></section>
                </div>

    ```javascript
    function createPresentation() {{
        const pptx = new PptxGenJS();
        const slide = pptx.addSlide();

        // EXTRACTED: background: #1A252F → '1A252F'
        slide.background = {{ color: '1A252F' }};

        // EXTRACTED: h1 + color: #FFFFFF → fontSize: 32, color: 'FFFFFF'
        slide.addText('Executive Summary', {{
            x: 0.5,
            y: 0.5,
            w: 9.0,
            h: 0.8,
            fontSize: 32,
            color: 'FFFFFF',
            bold: true
        }});

        // LEFT COLUMN: Problem section
        slide.addText('❓ Problem', {{
            x: 0.5,
            y: 1.5,
            w: 4.0,
            h: 0.5,
            fontSize: 18,
            color: 'E74C3C',
            bold: true
        }});

        slide.addText('Current search is inefficient', {{
            x: 0.5,
            y: 2.1,
            w: 4.0,
            h: 1.0,
            fontSize: 14,
            color: 'FFFFFF'
        }});

        // RIGHT COLUMN: Solution section
        slide.addText('💡 Solution', {{
            x: 5.25,
            y: 1.5,
            w: 4.0,
            h: 0.5,
            fontSize: 18,
            color: '2ECC71',
            bold: true
        }});

        slide.addText('AI-powered system', {{
            x: 5.25,
            y: 2.1,
            w: 4.0,
            h: 1.0,
            fontSize: 14,
            color: 'E0E0E0'
        }});

        return pptx.writeFile({{ fileName: '../generated_presentations/{slide_name}.pptx' }});
    }}
    ```

    HTML ANALYSIS PATTERNS:
    - <h1> tags → fontSize: 32-40, bold: true, main title positioning
    - <h2> tags → fontSize: 20-28, bold: true, section header positioning
    - <h3> tags → fontSize: 16-20, bold: true, subsection positioning
    - <p> tags → fontSize: 12-16, regular text
    - CSS classes like .grid, .columns → Use 2-column layout template
    - CSS classes like .hero, .title → Use centered title layout
    - inline style='color: #...' → Extract hex color, remove #
    - CSS background-color → slide.background color
    - font-weight: bold → bold: true
    - text-align: center → align: 'center'

    FORBIDDEN PATTERNS - DO NOT GENERATE:
    ❌ 'FFFFFF\' (backslash before closing quote)
    ❌ \'FFFFFF' (backslash before opening quote)
    ❌ color: \'red' (backslash in color values)
    ❌ text: \'content\' (backslashes around text)
    ❌ 'we'll' (unescaped apostrophes - use 'we\'ll')
    ❌ '* 'Popcorning': text' (nested single quotes)
    ❌ 'text with 'quotes' inside' (quotes inside quotes)
    ❌ color: 'FFFFFF }} (missing closing quote)
    ❌ 'unclosed string (missing closing quote)
    ❌ addText(Title Text, {{ (unquoted text)
    ❌ color: FFFFFF (unquoted color)
    ❌ {{ text: * Bullet point (unquoted text in array)

    ✅ CORRECT PATTERNS:
    ✅ 'FFFFFF' (simple quoted strings)
    ✅ color: 'red' (clean color values)
    ✅ text: 'content' (clean text strings)
    ✅ 'we\'ll' (properly escaped apostrophes)
    ✅ '* Popcorning: text' (no nested quotes)
    ✅ 'text with quotes inside' (remove inner quotes)
    ✅ color: 'FFFFFF' }} (properly closed quotes)
    ✅ addText('Title Text', {{ (quoted text)
    ✅ color: 'FFFFFF' (quoted color)
    ✅ {{ text: '* Bullet point' (quoted text in array)

    FILENAME REQUIREMENTS:
    - CRITICAL: Use EXACTLY this filename format: fileName: '../generated_presentations/{slide_name}.pptx'
    - For slide_name="{slide_name}", use: fileName: '../generated_presentations/{slide_name}.pptx'
    - DO NOT use 'presentation.pptx' or just '{slide_name}.pptx'
    - MUST include the full path: '../generated_presentations/{slide_name}.pptx'

    Generate ONLY the JavaScript function. Keep it simple and syntactically correct.
    """)

    response = await llm.call(query=prompt)
    
    # Extract code from response
    if isinstance(response, dict) and "response" in response:
        code = response["response"]
    elif isinstance(response, dict) and "text" in response:
        code = response["text"]
    else:
        code = str(response)
    
    # Clean and validate the code
    code = _clean_and_validate_js_code(code)
    
    return code


async def llm_html_to_pptxgenjs_combined(html_slides: List[Dict[str, Any]], llm) -> str:
    """
    Convert multiple HTML slides to a single PptxGenJS presentation

    Args:
        html_slides: List of slide dictionaries with 'html_content' key
        llm: LLM instance for translation

    Returns:
        JavaScript code for creating a multi-slide presentation
    """
    try:
        # Combine all HTML slides into analysis context
        combined_html = ""
        for i, slide_data in enumerate(html_slides):
            html_content = slide_data.get("html_content", "")
            combined_html += f"\n\n<!-- SLIDE {i+1} -->\n{html_content}"
        
        prompt = textwrap.dedent(f"""
        You are an expert JavaScript developer specializing in PptxGenJS. Convert these HTML slides to a multi-slide PptxGenJS presentation.

        ANALYSIS PHASE - For each slide extract:
        1. BACKGROUND: Look for background-color, background, body styles → Extract hex, remove #
        2. TITLE COLOR: Look for h1/h2 color styles → Extract hex, remove #
        3. TEXT COLOR: Look for p/div/span color styles → Extract hex, remove #
        4. LAYOUT TYPE: Detect if single-column, 2-column grid, or complex layout
        5. CONTENT HIERARCHY: Identify title, subtitles, bullet points, key content

        POSITIONING SYSTEM - Use these EXACT coordinates:
        - TITLE_Y = 0.8 (always for main titles)
        - CONTENT_START_Y = 1.8 (first content element)
        - Y_INCREMENT = 0.7 (spacing between elements)
        - MAX_Y = 4.5 (never exceed this boundary)
        - LEFT_COL_X = 0.5, RIGHT_COL_X = 5.25 (for 2-column layouts)
        - FULL_WIDTH_W = 9.0, COL_WIDTH_W = 4.0

        DYNAMIC STYLING RULES:
        - Title fontSize: 28-36 based on text length (shorter = larger)
        - Content fontSize: 14-18 based on hierarchy (h2=18, h3=16, p=14)
        - Colors: Use extracted colors, fallback to professional defaults
        - Bold: true for titles and headings, false for content

        GENERATE CODE STRUCTURE:
        ```javascript
        function createPresentation() {{
            const pptx = new PptxGenJS();

            // SLIDE 1
            const slide1 = pptx.addSlide();
            slide1.background = {{ color: 'EXTRACTED_BG_COLOR' }};

            // TITLE (always at y: 0.8)
            slide1.addText('EXTRACTED_TITLE', {{
                x: 0.5, y: 0.8, w: 9.0, h: 0.8,
                fontSize: DYNAMIC_SIZE, color: 'EXTRACTED_TITLE_COLOR', bold: true
            }});

            // CONTENT (start at y: 1.8, increment by 0.7)
            slide1.addText('EXTRACTED_CONTENT_1', {{
                x: DYNAMIC_X, y: 1.8, w: DYNAMIC_W, h: 0.6,
                fontSize: DYNAMIC_SIZE, color: 'EXTRACTED_TEXT_COLOR'
            }});

            return pptx.writeFile({{ fileName: 'presentation.pptx' }});
        }}
        ```

        CRITICAL CONSTRAINTS:
        1. Replace ALL DYNAMIC_* and EXTRACTED_* with actual literal values
        2. Use ONLY y positions: 0.8, 1.8, 2.5, 3.2, 3.9 (never exceed 4.5)
        3. For 2-column: Left x: 0.5, Right x: 5.25, both w: 4.0
        4. For single-column: x: 0.5, w: 9.0
        5. Colors must be literal strings without # (e.g., '2C3E50', not '#2C3E50')
        6. Use * for bullets, avoid Unicode characters
        7. Maximum 5 content elements per slide
        8. Keep text concise - max 80 characters per element

        COLOR FORMATTING RULES - CRITICAL:
        - Colors must be simple quoted strings: 'FFFFFF', '2C3E50', 'E0E0E0'
        - NO extra quotes or backslashes: 'FFFFFF\' is WRONG
        - NO hash symbols: '#FFFFFF' is WRONG
        - NO variables: BG_COLOR is WRONG
        - NO backslashes anywhere: \' is FORBIDDEN
        - CORRECT format: slide.background = {{ color: 'FFFFFF' }};

        STRING FORMATTING RULES - CRITICAL:
        - ALL text must be quoted: slide.addText('Title Text', {{ ... }})
        - ALL colors must be quoted: color: 'FFFFFF', color: '2C3E50'
        - ALL text in arrays must be quoted: {{ text: '* Bullet point', ... }}
        - NO unquoted text: addText(Title, {{ is WRONG
        - NO unquoted colors: color: FFFFFF is WRONG
        - For apostrophes in text, escape them: 'we\'ll' not 'we'll'
        - CORRECT: slide.addText('Don\'t use backslashes', {{ color: '2C3E50' }});

        HTML SLIDES TO ANALYZE:
        {combined_html}

        FORBIDDEN PATTERNS - DO NOT GENERATE:
        ❌ 'FFFFFF\' (backslash before closing quote)
        ❌ \'FFFFFF' (backslash before opening quote)
        ❌ color: \'red' (backslash in color values)
        ❌ text: \'content\' (backslashes around text)
        ❌ 'we'll' (unescaped apostrophes - use 'we\'ll')
        ❌ '* 'Popcorning': text' (nested single quotes)
        ❌ 'text with 'quotes' inside' (quotes inside quotes)

        ✅ CORRECT PATTERNS:
        ✅ 'FFFFFF' (simple quoted strings)
        ✅ color: 'red' (clean color values)
        ✅ text: 'content' (clean text strings)
        ✅ 'we\'ll' (properly escaped apostrophes)
        ✅ '* Popcorning: text' (no nested quotes)
        ✅ 'text with quotes inside' (remove inner quotes)

        Generate the complete function with all dynamic values replaced by literals.
        """)
        
        response = await llm.call(query=prompt)

        # Debug: Check what LLM returned
        print(f"🔍 LLM response type: {type(response)}")
        print(f"🔍 LLM response: {response}")

        # Clean up the response
        js_code = None
        if isinstance(response, dict) and "response" in response:
            js_code = response["response"]
        elif isinstance(response, dict) and "text" in response:
            js_code = response["text"]
        elif response is not None:
            js_code = str(response)

        # Check if we got valid code
        if js_code is None or js_code.strip() == "":
            raise ValueError(f"LLM returned empty or None response: {response}")

        # Clean and validate the code
        js_code = _clean_and_validate_js_code(js_code)
        
        return js_code
        
    except Exception as e:
        print(f"❌ Error in HTML-to-PptxGenJS translation: {e}")
        raise e


def _clean_and_validate_js_code(code: str) -> str:
    """
    Clean and validate JavaScript code using proven patterns from notebook experiments.
    Simple, targeted fixes that don't over-engineer or create new problems.
    """
    if not code:
        return ""

    # Clean up the response to remove any markdown code blocks
    code = code.replace('```javascript', '').replace('```js', '').replace('```', '')

    # RULE-BASED CLEANUP: Remove problematic statements
    # Remove import/require statements
    code = re.sub(r'.*\\b(import|require)\\b.*', '', code)

    # Remove function wrappers
    code = re.sub(r'.*function\\s+\\w+.*\\{.*', '', code)
    code = re.sub(r'^\\s*\\}\\s*$', '', code, flags=re.MULTILINE)

    # Remove export statements
    code = re.sub(r'.*(export|module\\.exports).*', '', code)

    # Basic validation to ensure proper color formatting
    # Find patterns like color: #FFFFFF or color: FFFFFF (without quotes)
    color_pattern = r'color:\s*([^\'\"]+)([,}])'
    code = re.sub(color_pattern, lambda m: f'color: \'{m.group(1).strip()}\'{m.group(2)}', code)

    # Find patterns like { color: #FFFFFF } or { color: FFFFFF } (without quotes)
    obj_color_pattern = r'{\s*color:\s*([^\'\"]+)([,}])'
    code = re.sub(obj_color_pattern, lambda m: f'{{ color: \'{m.group(1).strip()}\'{m.group(2)}', code)

    # Check for unclosed string literals
    def fix_unclosed_strings(code):
        lines = code.split('\n')
        for i, line in enumerate(lines):
            # Check for lines with odd number of single quotes (likely unclosed string)
            if line.count("'") % 2 != 0 and "'" in line:
                # Add closing quote at the end if it seems to be missing
                if not line.strip().endswith("'"):
                    lines[i] = line + "'"
        return '\n'.join(lines)

    code = fix_unclosed_strings(code)

    # Replace any variable references in color values with literal strings
    var_in_color_pattern = r'color:\s*\'([^\']*\$\{[^}]*\}[^\']*)\'([,}])'
    code = re.sub(var_in_color_pattern, lambda m: f'color: \'000000\'{m.group(2)}', code)

    var_name_pattern = r'color:\s*([a-zA-Z][a-zA-Z0-9_]*)([,}])'
    code = re.sub(var_name_pattern, lambda m: f'color: \'000000\'{m.group(2)}', code)

    # Fix Unicode bullet characters that cause Node.js parsing issues
    code = code.replace('•', '*')  # Replace Unicode bullet with ASCII asterisk
    code = code.replace('–', '-')  # Replace en-dash with ASCII hyphen
    code = code.replace(''', "'")  # Replace smart quote with ASCII quote
    code = code.replace(''', "'")  # Replace smart quote with ASCII quote
    code = code.replace('"', '"')  # Replace smart quote with ASCII quote
    code = code.replace('"', '"')  # Replace smart quote with ASCII quote

    # CRITICAL: Fix unescaped apostrophes in JavaScript strings
    # Simple but effective approach: escape common contractions
    contractions = [
        "don't", "we'll", "can't", "won't", "I'll", "you'll", "they'll",
        "it's", "that's", "what's", "here's", "there's", "let's",
        "he's", "she's", "who's", "where's", "when's", "how's", "why's",
        "I'm", "you're", "we're", "they're", "isn't", "aren't",
        "wasn't", "weren't", "hasn't", "haven't", "hadn't",
        "doesn't", "didn't", "wouldn't", "shouldn't", "couldn't"
    ]

    for contraction in contractions:
        # Replace unescaped contractions with escaped ones
        escaped_contraction = contraction.replace("'", "\\'")
        # Simple replacement - be careful not to double-escape
        if contraction in code and escaped_contraction not in code:
            code = code.replace(contraction, escaped_contraction)

    # Fix common syntax errors
    code = re.sub(r'color:\s*\'([^\']*)\',\'', r'color: \'\1\'', code)  # Extra comma after color
    code = re.sub(r'(bold|italic):\s*true,\s*\);', r'\1: true });', code)  # Trailing comma before );
    code = re.sub(r',\s*\);', ' });', code)  # ,); → });
    code = re.sub(r',\s*,', ',', code)  # Double commas

    # Ensure function structure
    code = code.strip()
    if not code.startswith('function createPresentation()'):
        if 'const pptx = new PptxGenJS()' not in code:
            code = 'const pptx = new PptxGenJS();\n' + code
        if 'return pptx.writeFile' not in code:
            code = code + '\n\nreturn pptx.writeFile({ fileName: \'presentation.pptx\' });'
        code = f'function createPresentation() {{\n    {code.replace(chr(10), chr(10) + "    ")}\n}}'

    return code


# PowerPoint Generation Pipeline Functions








    # CRITICAL: Fix nested single quotes that break JavaScript
    # Pattern: '* 'Popcorning': text' → '* Popcorning: text'
    # Remove inner single quotes that are inside outer single quotes
    def fix_nested_quotes(match):
        full_string = match.group(0)
        content = match.group(1)
        # Remove any single quotes inside the content
        cleaned_content = content.replace("'", "")
        return f"'{cleaned_content}'"

    # Match single-quoted strings that contain other single quotes
    code = re.sub(r"'([^']*'[^']*)'", fix_nested_quotes, code)

    # Also handle multiple nested quotes
    # Keep applying the fix until no more nested quotes are found
    max_iterations = 5
    for i in range(max_iterations):
        old_code = code
        code = re.sub(r"'([^']*'[^']*)'", fix_nested_quotes, code)
        if code == old_code:  # No more changes
            break

    # CRITICAL: Fix unclosed string literals
    # Pattern: color: 'FFFFFF } → color: 'FFFFFF' }
    # Look for patterns where quotes are not properly closed

    # Fix unclosed color values specifically
    code = re.sub(r"color:\s*'([A-Fa-f0-9]{6})\s*([,}])", r"color: '\1'\2", code)
    code = re.sub(r"color:\s*'([A-Fa-f0-9]{3})\s*([,}])", r"color: '\1'\2", code)

    # Fix general unclosed quotes before common delimiters
    code = re.sub(r"'([^']*)\s*([,};\)])", r"'\1'\2", code)

    # Fix unclosed quotes at end of lines
    code = re.sub(r"'([^']*)\s*$", r"'\1'", code, flags=re.MULTILINE)

    # CRITICAL: Fix unquoted color values
    # Pattern: color: 2C3E50 → color: '2C3E50'
    code = re.sub(r'color:\s*([A-Fa-f0-9]{6})([,\s])', r"color: '\1'\2", code)
    code = re.sub(r'color:\s*([A-Fa-f0-9]{3})([,\s])', r"color: '\1'\2", code)

    # COMPREHENSIVE: Fix all unquoted text and color patterns

    # Fix unquoted text in addText calls
    # Pattern: addText(Some Text, { → addText('Some Text', {
    code = re.sub(r'addText\(([^\'"][^,]*),\s*\{', r"addText('\1', {", code)

    # Fix unquoted text in array elements
    # Pattern: { text: * Bullet point, → { text: '* Bullet point',
    code = re.sub(r'{\s*text:\s*([^\'"][^,]*),', r"{ text: '\1',", code)

    # Fix unquoted colors in all contexts
    # Pattern: color: FFFFFF → color: 'FFFFFF'
    code = re.sub(r'color:\s*([A-Fa-f0-9]{6})([,\s}])', r"color: '\1'\2", code)
    code = re.sub(r'color:\s*([A-Fa-f0-9]{3})([,\s}])', r"color: '\1'\2", code)

    # Fix unquoted background colors
    # Pattern: { color: FFFFFF } → { color: 'FFFFFF' }
    code = re.sub(r'{\s*color:\s*([A-Fa-f0-9]{6})\s*}', r"{ color: '\1' }", code)
    code = re.sub(r'{\s*color:\s*([A-Fa-f0-9]{3})\s*}', r"{ color: '\1' }", code)

    # Fix unquoted align values
    # Pattern: align: center → align: 'center'
    code = re.sub(r'align:\s*(center|left|right)([,\s}])', r"align: '\1'\2", code)

    # Fix unquoted path values in images
    # Pattern: path: https://... → path: 'https://...'
    code = re.sub(r'path:\s*(https?://[^,\s}]+)', r"path: '\1'", code)

    # CRITICAL: Fix broken strings with commas
    # Pattern: '* American: Short', smooth coat. → '* American: Short, smooth coat.'
    # Look for strings that end with quote-comma followed by unquoted text
    def fix_broken_comma_strings(match):
        first_part = match.group(1)
        second_part = match.group(2).strip()
        # Combine them into one string
        return f"'{first_part}, {second_part}'"

    # Match pattern: 'text', more text
    code = re.sub(r"'([^']*)',\s*([^,{]*),", fix_broken_comma_strings, code)

    # CRITICAL: Fix unclosed string literals
    # Pattern: color: 'FFFFFF } → color: 'FFFFFF' }
    # Look for patterns where quotes are not properly closed

    # Fix unclosed color values specifically
    code = re.sub(r"color:\s*'([A-Fa-f0-9]{6})\s*([,}])", r"color: '\1'\2", code)
    code = re.sub(r"color:\s*'([A-Fa-f0-9]{3})\s*([,}])", r"color: '\1'\2", code)

    # Fix general unclosed quotes before common delimiters
    code = re.sub(r"'([^']*)\s*([,};\)])", r"'\1'\2", code)

    # Fix unclosed quotes at end of lines
    code = re.sub(r"'([^']*)\s*$", r"'\1'", code, flags=re.MULTILINE)

    # Additional simple fixes for common quote patterns
    # Fix: 'text 'word' more' → 'text word more'
    code = re.sub(r"'([^']*)'([^']*)'([^']*)'", r"'\1\2\3'", code)

    # Fix specific patterns we've seen
    code = code.replace("'Popcorning'", "Popcorning")  # Remove quotes around specific terms
    code = code.replace("'Guinea Pig'", "Guinea Pig")
    code = code.replace("'Cavia porcellus'", "Cavia porcellus")

    # Fix common PptxGenJS syntax errors

    # Fix problematic addShape syntax
    # Fix: pptx.ShapeType.oval -> 'oval'
    code = re.sub(r'pptx\.ShapeType\.(\w+)', r"'\1'", code)
    code = re.sub(r'pptx\.shapes\.(\w+)', r"'\1'", code)

    # Ensure all addShape calls have required w and h properties
    def fix_addshape_syntax(match):
        full_match = match.group(0)
        # Basic validation - if it doesn't have both w: and h:, it might cause issues
        if 'w:' in full_match and 'h:' in full_match:
            return full_match
        else:
            print(f"⚠️ Warning: addShape call might be missing w or h property: {full_match[:50]}...")
            return full_match

    code = re.sub(r'\.addShape\([^)]*\)', fix_addshape_syntax, code)

    print("🔧 Fixed addShape syntax (ShapeType references -> string names)")

    # Fix nested array syntax in addText calls - THIS IS THE MAIN CAUSE OF OVERLAPPING TEXT
    def fix_nested_arrays(match):
        full_match = match.group(0)
        array_content = match.group(1)

        # Check if this looks like a nested array (contains [ inside the array)
        if array_content.count('[') > 1:  # More than just the outer array
            print("🔧 CRITICAL: Fixing nested array in addText - this causes overlapping text in PowerPoint")

            # Simple fix: Convert nested array to simple string
            # Extract all text content and combine into a single string
            text_parts = re.findall(r"text:\s*['\"]([^'\"]*)['\"]", array_content)
            if text_parts:
                combined_text = ' '.join(text_parts)
                # Return a simple addText call with the combined text
                return f"addText('{combined_text}'"
            else:
                print("⚠️ Could not extract text from nested array, removing call")
                return "// Removed invalid nested array addText call"

        return full_match

    code = re.sub(r'addText\(\s*(\[.*?\])\s*,', fix_nested_arrays, code, flags=re.DOTALL)

    # Fix mixed array syntax: ['string', {text: 'object'}] -> [{text: 'string', options: {}}, {text: 'object', options: {}}]
    def fix_mixed_array_syntax(match):
        array_content = match.group(1)
        # This is a complex fix - for now, just warn about potential issues
        if "'" in array_content and '"text":' in array_content:
            print("⚠️ Warning: Detected potential mixed array syntax in PptxGenJS code")
        return match.group(0)

    code = re.sub(r'addText\(\[(.*?)\]', fix_mixed_array_syntax, code, flags=re.DOTALL)
    
    # Fix out-of-bounds x positions (keep within 0.5-9.5)
    def fix_x_position(match):
        try:
            value = float(match.group(1))
            fixed_value = min(9.5, max(0.5, value))
            return f'x: {fixed_value}'
        except:
            return match.group(0)
    
    code = re.sub(r'x:\s*([0-9]+\.?[0-9]*)', fix_x_position, code)
    
    # CRITICAL: Fix out-of-bounds y positions (enforce strict bounds based on lessons learned)
    def fix_y_position(match):
        try:
            value = float(match.group(1))
            # Enforce strict bounds based on our error analysis
            if value <= 1.0:
                return 'y: 0.8'  # Title area
            elif value <= 2.2:
                return 'y: 1.8'  # First content
            elif value <= 2.9:
                return 'y: 2.5'  # Second content
            elif value <= 3.6:
                return 'y: 3.2'  # Third content
            else:
                return 'y: 3.9'  # Fourth content (max safe position)
        except:
            return 'y: 1.8'  # Safe fallback

    code = re.sub(r'y:\s*([0-9]+\.?[0-9]*)', fix_y_position, code)

    # EMERGENCY: Replace any remaining out-of-bounds positions
    code = re.sub(r'y:\s*([5-9][0-9]*\.?[0-9]*)', 'y: 3.9', code)  # 5+ → 3.9
    code = re.sub(r'y:\s*([1-9][0-9]+\.?[0-9]*)', 'y: 3.9', code)  # 10+ → 3.9
    print("🔧 Fixed positioning bounds")

    # Fix overlapping Y positions - critical for preventing text stacking in PowerPoint
    addtext_matches = list(re.finditer(r'addText\([^}]*y:\s*([0-9.]+)[^}]*\}', code))

    if len(addtext_matches) > 1:
        y_positions = []
        for match in addtext_matches:
            try:
                y_pos = float(match.group(1))
                y_positions.append((y_pos, match))
            except ValueError:
                continue

        # Check for overlapping positions (within 0.6 inches)
        overlaps_found = any(
            abs(y1 - y2) < 0.6
            for i, (y1, _) in enumerate(y_positions)
            for y2, _ in y_positions[i+1:]
        )

        if overlaps_found:
            print("🔧 CRITICAL: Detected overlapping Y positions - fixing to prevent text stacking")
            # Sort by Y position and reassign with proper spacing
            y_positions.sort(key=lambda x: x[0])
            current_y = 1.8  # Start content area

            for original_y, match in y_positions:
                old_y_str = f'y: {match.group(1)}'
                new_y_str = f'y: {current_y:.1f}'
                code = code.replace(old_y_str, new_y_str, 1)
                current_y += 0.8  # Space elements 0.8 inches apart

    print("🔧 Fixed overlapping Y positions")

    # Ensure complete function structure
    if not code.startswith('function createPresentation()'):
        # Basic function wrapper if missing
        if 'const pptx = new PptxGenJS()' not in code:
            code = 'const pptx = new PptxGenJS();\n' + code
        if 'return pptx.writeFile' not in code:
            code = code + '\n\nreturn pptx.writeFile({ fileName: \'presentation.pptx\' });'
        
        # Wrap in function
        code = f'function createPresentation() {{\n    {code.replace(chr(10), chr(10) + "    ")}\n}}'
    
    return code


# PowerPoint Generation Pipeline Functions

async def generate_powerpoint_from_html(html_slides: List[Dict[str, Any]], llm) -> Dict[str, str]:
    """
    Complete PowerPoint generation pipeline from HTML slides to actual .pptx file

    Args:
        html_slides: List of slide dictionaries with 'html_content' key
        llm: LLM instance for translation

    Returns:
        Dict with 'presentation_js', 'pptx_filename', 'pptx_file_path', 'download_url'
    """
    try:
        print(f"🔄 Starting PowerPoint generation pipeline for {len(html_slides)} slides")

        # Step 1: Convert HTML slides to PptxGenJS code
        presentation_js = await llm_html_to_pptxgenjs_combined(html_slides, llm)

        # Step 2: Execute with Node.js to create actual PowerPoint file
        result = execute_pptxgenjs_code(presentation_js)

        # Step 3: Return complete result
        return {
            'presentation_js': presentation_js,
            'pptx_filename': result['pptx_filename'],
            'pptx_file_path': result['pptx_file_path'],
            'download_url': f"/download-presentation/{result['pptx_filename']}"
        }

    except Exception as e:
        print(f"❌ Error in PowerPoint generation pipeline: {e}")
        raise e


def execute_pptxgenjs_code(presentation_js: str) -> Dict[str, str]:
    """
    Execute PptxGenJS code with Node.js to create actual PowerPoint file

    Args:
        presentation_js: JavaScript code for creating the presentation

    Returns:
        Dict with 'pptx_filename' and 'pptx_file_path'
    """
    try:
        # Generate unique filenames
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        js_filename = f"presentation_{timestamp}.js"
        pptx_filename = f"presentation_{timestamp}.pptx"

        # Setup directories - use system temp directory for GCP Cloud Run compatibility
        system_temp_dir = tempfile.gettempdir()
        temp_dir = os.path.join(system_temp_dir, "temp_presentations")
        pptx_output_dir = os.path.join(system_temp_dir, "pptx_output")
        experimentation_dir = os.path.join(system_temp_dir, "experimentation")

        os.makedirs(temp_dir, exist_ok=True)
        os.makedirs(pptx_output_dir, exist_ok=True)
        os.makedirs(experimentation_dir, exist_ok=True)

        # File paths
        js_file_path = os.path.join(temp_dir, js_filename)
        pptx_file_path = os.path.join(temp_dir, pptx_filename)

        # Prepare JavaScript code for Node.js execution
        node_js_code = _prepare_nodejs_code(presentation_js, pptx_filename)

        # Write JavaScript file
        with open(js_file_path, 'w', encoding='utf-8') as f:
            f.write(node_js_code)
        print(f"✅ Generated presentation JavaScript: {js_file_path}")

        # Execute with Node.js
        _execute_nodejs_script(js_filename, experimentation_dir)

        # Find and copy the generated PowerPoint file
        _handle_generated_pptx_file(pptx_filename, experimentation_dir, pptx_output_dir, pptx_file_path)

        # Cleanup
        _cleanup_temp_files(js_filename, experimentation_dir)

        return {
            'pptx_filename': pptx_filename,
            'pptx_file_path': pptx_file_path
        }

    except Exception as e:
        print(f"❌ Error executing PptxGenJS code: {e}")
        raise e


# Helper Functions for PowerPoint Generation

def _prepare_nodejs_code(presentation_js: str, pptx_filename: str) -> str:
    """Prepare JavaScript code for Node.js execution with correct filename"""
    # Fix filename references in the generated code
    presentation_js_fixed = presentation_js.replace(
        "fileName: 'presentation.pptx'",
        f"fileName: '{pptx_filename}'"
    )
    presentation_js_fixed = presentation_js_fixed.replace(
        "fileName: 'llm-generated-presentation.pptx'",
        f"fileName: '{pptx_filename}'"
    )

    # Create complete Node.js script
    node_js_code = f"""
const PptxGenJS = require('pptxgenjs');

{presentation_js_fixed}

// Execute the presentation creation
createPresentation()
    .then(() => {{
        console.log('PowerPoint file generated successfully!');
        console.log('File saved as: {pptx_filename}');
        process.exit(0);
    }})
    .catch(error => {{
        console.error('Error generating presentation:', error);
        process.exit(1);
    }});
"""
    return node_js_code


def _execute_nodejs_script(js_filename: str, experimentation_dir: str) -> None:
    """Execute the JavaScript file with Node.js"""
    # Copy JS file to experimentation directory for execution
    system_temp_dir = tempfile.gettempdir()
    temp_presentations_dir = os.path.join(system_temp_dir, "temp_presentations")
    js_source = os.path.join(temp_presentations_dir, js_filename)
    js_target = os.path.join(experimentation_dir, js_filename)
    shutil.copy2(js_source, js_target)

    try:
        # Execute Node.js script
        result = subprocess.run(
            ["node", js_filename],
            cwd=experimentation_dir,
            check=True,
            capture_output=True,
            text=True,
            timeout=30  # 30 second timeout
        )

        print(f"✅ Node.js execution output: {result.stdout}")

    except subprocess.CalledProcessError as e:
        print(f"❌ Node.js execution failed with exit code {e.returncode}")
        print(f"❌ STDOUT: {e.stdout}")
        print(f"❌ STDERR: {e.stderr}")

        # Also print the first few lines of the JS file for debugging
        try:
            with open(js_target, 'r', encoding='utf-8') as f:
                js_content = f.read()
                lines = js_content.split('\n')[:10]  # First 10 lines
                print(f"❌ First 10 lines of generated JS file:")
                for i, line in enumerate(lines, 1):
                    print(f"   {i}: {line}")
        except Exception as file_error:
            print(f"❌ Could not read JS file for debugging: {file_error}")

        raise Exception(f"Node.js execution failed: {e.stderr}")

    except subprocess.TimeoutExpired as e:
        print(f"❌ Node.js execution timed out after 30 seconds")
        raise Exception("Node.js execution timed out")


def _handle_generated_pptx_file(pptx_filename: str, experimentation_dir: str,
                                pptx_output_dir: str, pptx_file_path: str) -> None:
    """Find and copy the generated PowerPoint file to appropriate locations"""
    # Wait for file system to sync
    time.sleep(0.5)

    # Check for the generated PPTX file with multiple possible names
    possible_files = [
        os.path.join(experimentation_dir, pptx_filename),
        os.path.join(experimentation_dir, "presentation.pptx"),
        os.path.join(experimentation_dir, "llm-generated-presentation.pptx")
    ]

    found_file = None
    for possible_file in possible_files:
        if os.path.exists(possible_file):
            found_file = possible_file
            break

    if found_file:
        # Copy to backup directory
        pptx_backup_path = os.path.join(pptx_output_dir, pptx_filename)
        shutil.copy2(found_file, pptx_backup_path)

        # Copy to temp directory for download
        shutil.copy2(found_file, pptx_file_path)

        print(f"✅ PowerPoint file generated: {pptx_file_path}")
        print(f"✅ PowerPoint file backed up: {pptx_backup_path}")
    else:
        # Debug: List available files
        exp_files = [f for f in os.listdir(experimentation_dir) if f.endswith('.pptx')]
        print(f"❌ Expected file not found. Available .pptx files: {exp_files}")
        raise Exception(f"PowerPoint file was not generated. Expected: {pptx_filename}")


def _cleanup_temp_files(js_filename: str, experimentation_dir: str) -> None:
    """Clean up temporary JavaScript files"""
    js_temp_path = os.path.join(experimentation_dir, js_filename)
    if os.path.exists(js_temp_path):
        os.remove(js_temp_path)
        print(f"🧹 Cleaned up temporary file: {js_temp_path}")
