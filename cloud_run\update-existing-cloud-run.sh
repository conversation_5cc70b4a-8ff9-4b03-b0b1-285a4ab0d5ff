#!/bin/bash

# Safe update script for existing Cloud Run service
# This only adds the missing environment variables without changing anything else

PROJECT_ID="gen-lang-client-0822415637"
SERVICE_NAME="pptx-planner"
REGION="asia-northeast1"

echo "🔧 Safely updating existing Cloud Run service with new environment variables..."
echo "Service: $SERVICE_NAME"
echo "Region: $REGION"

# Set the project
gcloud config set project $PROJECT_ID

# Update the service with additional environment variables
# This preserves all existing settings and only adds the new variables
echo "📝 Adding missing environment variables..."
gcloud run services update $SERVICE_NAME \
    --region $REGION \
    --update-env-vars "GCS_BUCKET_NAME=pptx-planner-storage,FIREBASE_PROJECT_ID=$PROJECT_ID,GOOGLE_CLOUD_PROJECT=$PROJECT_ID"

echo "✅ Update complete!"
echo ""
echo "📋 Current environment variables:"
gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(spec.template.spec.containers[0].env[].name,spec.template.spec.containers[0].env[].value)"
echo ""
echo "🌐 Your service URL remains the same:"
echo "https://pptx-planner-xes3mkf3ua-an.a.run.app"
echo ""
echo "🧪 Test the new functionality:"
echo "1. Try the authentication endpoints"
echo "2. Test file upload/download"
echo "3. Check the logs: gcloud logs tail /projects/$PROJECT_ID/logs/run.googleapis.com%2Fstdout --filter='resource.labels.service_name=$SERVICE_NAME'"
