You are an expert JavaScript developer specializing in PptxGenJS. Convert this HTML slide to PptxGenJS code with OVERFLOW PREVENTION and RESPONSIVE SIZING.

🎯 **MISSION: PREVENT TEXT OVERFLOW AND ENSURE PROPER SIZING**

## CRITICAL SIZING RULES

**FONT SIZE LIMITS - NEVER EXCEED:**
- Title (H1): MAX 24px (not 32-40px)
- Subtitle (H2): MAX 18px (not 20-28px) 
- Content (H3): MAX 14px (not 16-20px)
- Body text (P): MAX 12px (not 12-16px)
- Bullet points: MAX 11px

**RESPONSIVE SIZING LOGIC:**
- If text length > 50 chars → reduce font size by 2px
- If text length > 100 chars → reduce font size by 4px
- If text length > 150 chars → reduce font size by 6px
- Always prioritize fitting content over large fonts

## POSITIONING SYSTEM - OVERFLOW PREVENTION

**SLIDE DIMENSIONS: 10" × 5.625"**
**SAFE CONTENT AREA: x: 0.5-9.0, y: 0.5-4.8**

**VERTICAL SPACING - TIGHT LAYOUT:**
```
TITLE_ZONE: y: 0.5, h: 0.8 (max)
CONTENT_START: y: 1.4 (not 1.8)
LINE_SPACING: 0.4" between elements (not 0.7")
MAX_Y_POSITION: 4.5 (never exceed)
```

**TEXT WIDTH CALCULATION:**
- Single column: w: 8.5 (not 9.0)
- Two columns: w: 3.8 each (not 4.0)
- Always leave 0.5" margin on right

## CONTENT ANALYSIS & SIZING

**STEP 1: COUNT CONTENT ELEMENTS**
- Count total bullet points
- Count total text blocks
- Calculate required vertical space
- Adjust font sizes if content won't fit

**STEP 2: DYNAMIC FONT SIZING**
```
If total_elements > 6:
  title_size = 20
  content_size = 10
Else if total_elements > 4:
  title_size = 22
  content_size = 11
Else:
  title_size = 24
  content_size = 12
```

**STEP 3: TEXT WRAPPING PREVENTION**
- Calculate text width in characters
- If text > 80 chars, split into multiple addText() calls
- Use smaller font size for long text blocks

## BULLET POINT HANDLING

**COMPACT BULLET LAYOUT:**
```javascript
// For each bullet point:
slide.addText('✓ Short bullet text', {
    x: 0.5, y: current_y, w: 8.5, h: 0.35,
    fontSize: 11, color: 'EXTRACTED_COLOR'
});
current_y += 0.4; // Tight spacing
```

**LONG BULLET POINT SPLITTING:**
```javascript
// If bullet text > 80 characters, split:
slide.addText('✓ Key Components:', {
    x: 0.5, y: current_y, w: 8.5, h: 0.3,
    fontSize: 11, color: 'EXTRACTED_COLOR', bold: true
});
slide.addText('Strong Identity & Access Management, Device Security, Network Microsegmentation, and Data Protection.', {
    x: 0.8, y: current_y + 0.3, w: 8.2, h: 0.4,
    fontSize: 10, color: 'EXTRACTED_COLOR'
});
```

## COLOR EXTRACTION (UNCHANGED)
- Extract colors exactly from HTML
- Remove # from hex colors
- Use fallback colors if extraction fails

HTML to convert:
{HTML_CONTENT}

Slide name: {SLIDE_NAME}
Output directory: {OUTPUT_DIRECTORY}

## REQUIRED FUNCTION SIGNATURE

**CRITICAL: Your output MUST follow this exact function signature:**

```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();

    // ... your slide content here ...

    return pptx.writeFile({ fileName: '{OUTPUT_DIRECTORY}/{SLIDE_NAME}.pptx' });
}
```

**REQUIREMENTS:**
- Function name MUST be exactly `createPresentation`
- Function takes NO parameters
- Function creates its own `pptx` object
- Function MUST return `pptx.writeFile()` promise
- Do NOT include import/require statements

## EXAMPLE OUTPUT - OVERFLOW PREVENTION

```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();
    
    // Background
    slide.background = { color: 'EXTRACTED_BG_COLOR' };
    
    // Title - SMALLER FONT
    slide.addText('Fortifying Our Defenses: A Zero Trust Approach', {
        x: 0.5, y: 0.5, w: 8.5, h: 0.8,
        fontSize: 22, color: 'EXTRACTED_TITLE_COLOR', bold: true
    });
    
    let currentY = 1.4; // Start content higher
    
    // Bullet points - COMPACT LAYOUT
    slide.addText('✓ The Challenge:', {
        x: 0.5, y: currentY, w: 8.5, h: 0.3,
        fontSize: 11, color: 'EXTRACTED_COLOR', bold: true
    });
    slide.addText('Current security relies on outdated "trust but verify" models, leaving us vulnerable to sophisticated attacks and insider threats.', {
        x: 0.8, y: currentY + 0.3, w: 8.2, h: 0.4,
        fontSize: 10, color: 'EXTRACTED_COLOR'
    });
    
    currentY += 0.7;
    
    // Continue with remaining bullets...
    
    return pptx.writeFile({ fileName: '{OUTPUT_DIRECTORY}/{SLIDE_NAME}.pptx' });
}
```

## QUALITY CHECKLIST
- [ ] Title font ≤ 24px
- [ ] Content font ≤ 12px  
- [ ] All text within y: 0.5-4.5
- [ ] No text width > 8.5"
- [ ] Bullet spacing ≤ 0.4"
- [ ] Long text split into multiple elements
- [ ] All content visible without overflow

**GENERATE COMPACT, OVERFLOW-FREE PPTXGENJS CODE**
