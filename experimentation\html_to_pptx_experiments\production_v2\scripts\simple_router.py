#!/usr/bin/env python3
"""
Simple Intelligent HTML-to-PowerPoint Prompt Router
Automatically selects optimal conversion strategy based on HTML pattern analysis
"""

import sys
import re
from pathlib import Path
from typing import Dict, Tuple

# Import lightweight HTML analyzer for richer routing signals
try:
    from .html_analyzer import analyze_html
except Exception:
    # Fallback for relative import when executed as script
    from html_analyzer import analyze_html

class SimpleIntelligentRouter:
    """
    Pattern-based HTML analysis and prompt routing system
    """

    def __init__(self):
        self.available_prompts = {
            'title_slide': {
                'description': 'Professional title slide with large fonts and centered layout',
                'patterns': ['title', 'cover', 'intro', 'introduction', 'welcome']
            },
            'agenda_slide': {
                'description': 'Clear agenda/outline slide with larger fonts for readability',
                'patterns': ['agenda', 'outline', 'overview', 'contents', 'topics', 'schedule']
            },
            'table_expert': {
                'description': 'Advanced table, timeline, and complex layout conversion',
                'patterns': ['<table', 'timeline', 'phase', 'gantt', 'left-column', 'right-column']
            },
            'chart_expert': {
                'description': 'Advanced chart, graph, and data visualization conversion',
                'patterns': ['chart', 'graph', 'data', 'percentage', 'statistics', 'metrics']
            },
            'balanced': {
                'description': 'Readable fonts with overflow prevention',
                'patterns': ['simple', 'standard', 'basic']
            },
            'overflow_fix': {
                'description': 'Aggressive overflow prevention for content-heavy slides',
                'patterns': ['content-heavy', 'high-density']
            },
            'ultra_safe': {
                'description': 'Ultra-safe positioning with zero overflow guarantee',
                'patterns': ['overflow-risk', 'complex-layout', 'problematic']
            }
        }

    def detect_slide_type(self, html_content: str, slide_name: str) -> str:
        """
        Detect the type of slide based on content and naming patterns
        """
        content_lower = html_content.lower()
        name_lower = slide_name.lower()

        # Title slide detection FIRST (most specific)
        title_indicators = ['title', 'cover', 'intro', 'introduction', 'welcome']
        if any(indicator in name_lower for indicator in title_indicators):
            return 'title'

        # Check for title slide content patterns (very restrictive)
        if ('<h1' in html_content and content_lower.count('<h') <= 2 and
            len(html_content) < 1000):  # Short, simple content
            return 'title'

        # Agenda slide detection (more restrictive now)
        agenda_indicators = ['agenda', 'outline', 'overview', 'contents', 'topics', 'schedule']
        if any(indicator in name_lower for indicator in agenda_indicators):
            # Additional check: must be simple content for agenda
            if len(html_content) < 3000:  # Simple agenda slides only
                return 'agenda'

        # Check for simple agenda content patterns (much more restrictive)
        list_indicators = ['<ol', '<ul', '<li']
        agenda_content_words = ['agenda', 'outline', 'topics']

        has_simple_lists = (sum(content_lower.count(indicator) for indicator in list_indicators) >= 3 and
                           len(html_content) < 2000)  # Simple lists only
        has_agenda_content = any(word in content_lower for word in agenda_content_words)

        # Only classify as agenda if it's simple content with clear agenda indicators
        if has_simple_lists and has_agenda_content and len(html_content) < 3000:
            return 'agenda'

        # Default to general content slide (most complex content goes here)
        return 'general'

    def analyze_html_patterns(self, html_content: str, slide_name: str = "unknown") -> Dict:
        """
        Analyze HTML content using pattern matching plus lightweight analyzer
        """
        print("🧠 Analyzing HTML patterns...")

        # Analyzer pass (colors, visuals, layout)
        analyzer = analyze_html(html_content)
        flags = analyzer.get('flags', {})

        # Detect slide type first
        slide_type = self.detect_slide_type(html_content, slide_name)
        print(f"   🎯 Slide type detected: {slide_type}")

        html_lower = html_content.lower()

        # Pattern detection
        patterns_found = [slide_type]
        scores = {}

        # Add slide type specific prompts to available options
        if slide_type == 'title':
            scores['title_slide'] = 100  # Highest priority for title slides
        elif slide_type == 'agenda':
            scores['agenda_slide'] = 100  # Highest priority for agenda slides
        else:  # general content
            # For complex general content, use ultra_safe as default
            content_length = len(html_content)
            if content_length > 5000:  # Complex content
                scores['ultra_safe'] = 90  # High priority for complex content
            else:
                scores['balanced'] = 80  # Medium priority for simpler general content

        # Simplified visual complexity detection (no color analysis complexity)
        if flags.get('complex_visuals'):
            patterns_found.append('complex_visuals')
            scores['ultra_safe'] = max(scores.get('ultra_safe', 0), 70)
            print("   ✅ Visual element density detected")

        # Table patterns
        if '<table' in html_lower:
            patterns_found.append('html_table')
            scores['table_expert'] = scores.get('table_expert', 0) + 40
            print("   ✅ HTML table detected")

        # Timeline/process patterns
        timeline_keywords = ['timeline', 'phase', 'step', 'gantt', 'roadmap', 'schedule']
        if any(keyword in html_lower for keyword in timeline_keywords):
            patterns_found.append('timeline')
            scores['table_expert'] = scores.get('table_expert', 0) + 35
            print("   ✅ Timeline/process pattern detected")

        # Two-column layout patterns
        column_keywords = ['left-column', 'right-column', 'col-', 'grid', 'flex']
        if any(keyword in html_lower for keyword in column_keywords):
            patterns_found.append('two_column')
            scores['table_expert'] = scores.get('table_expert', 0) + 25
            print("   ✅ Multi-column layout detected")

        # Financial/data patterns
        financial_keywords = ['$', 'cost', 'price', 'budget', 'roi', 'revenue', '%']
        if any(keyword in html_lower for keyword in financial_keywords):
            patterns_found.append('financial_data')
            scores['table_expert'] = scores.get('table_expert', 0) + 20
            print("   ✅ Financial/data content detected")

        # Chart/visualization patterns
        chart_keywords = ['chart', 'graph', 'data', 'percentage', 'statistics', 'metrics',
                         'canvas', 'svg', 'visualization', 'analytics', 'dashboard']
        if any(keyword in html_lower for keyword in chart_keywords):
            patterns_found.append('chart_data')
            scores['chart_expert'] = scores.get('chart_expert', 0) + 30
            print("   ✅ Chart/visualization content detected")

        # Percentage-heavy content (good for pie charts)
        percentage_count = html_lower.count('%')
        if percentage_count > 3:
            patterns_found.append('percentage_heavy')
            scores['chart_expert'] = scores.get('chart_expert', 0) + 25
            print(f"   ✅ High percentage content detected ({percentage_count} instances)")

        # Content density analysis
        content_length = len(html_content)
        text_blocks = len(re.findall(r'<p[^>]*>|<li[^>]*>|<div[^>]*>', html_content))

        print(f"   📊 Content length: {content_length} characters")
        print(f"   📊 Text blocks: {text_blocks}")

        if content_length > 8000:
            patterns_found.append('content_heavy')
            scores['overflow_fix'] = scores.get('overflow_fix', 0) + 30
            print("   ⚠️ High content density detected")
        elif content_length > 5000:
            scores['overflow_fix'] = scores.get('overflow_fix', 0) + 15

        # Default to balanced if no specific patterns
        if not scores:
            scores['balanced'] = 50
            print("   📝 Standard content pattern")

        return {
            'patterns_found': patterns_found,
            'scores': scores,
            'content_metrics': {
                'length': content_length,
                'text_blocks': text_blocks
            },
            'analysis': analyzer,
        }

    def select_optimal_prompt(self, analysis: Dict) -> Tuple[str, int]:
        """
        Select the best prompt based on analysis scores
        """
        scores = analysis['scores']
        
        if not scores:
            return 'balanced', 50
        
        # Find highest scoring prompt
        best_prompt = max(scores.keys(), key=lambda k: scores[k])
        confidence = min(scores[best_prompt], 95)  # Cap at 95%
        
        print(f"🎯 Prompt selection scores:")
        for prompt, score in sorted(scores.items(), key=lambda x: x[1], reverse=True):
            print(f"   {prompt}: {score}")
        
        return best_prompt, confidence
    
    def route_html_file(self, html_file: str) -> Tuple[str, int]:
        """
        Main routing function: analyze HTML file and recommend prompt
        """
        print(f"🚀 Intelligent HTML-to-PowerPoint Routing")
        print(f"📄 Input: {html_file}")
        print("=" * 60)
        
        # Load HTML content
        html_path = Path(html_file)
        if not html_path.exists():
            # Try relative to current directory
            html_path = Path("generated_html") / html_file
            if not html_path.exists():
                raise FileNotFoundError(f"HTML file not found: {html_file}")
        
        with open(html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Analyze patterns
        analysis = self.analyze_html_patterns(html_content)
        
        # Select optimal prompt
        optimal_prompt, confidence = self.select_optimal_prompt(analysis)

        # Use natural routing - no hardcoded overrides
        # Let the analysis determine the best prompt based on content patterns

        print(f"\n🎯 RECOMMENDATION:")
        print(f"   Optimal prompt: {optimal_prompt}")
        print(f"   Confidence: {confidence}%")
        print(f"   Detected patterns: {', '.join(analysis['patterns_found'])}")

        return optimal_prompt, confidence

    def recommend_prompt(self, patterns: Dict) -> Tuple[str, int]:
        """
        Recommend optimal prompt based on detected patterns
        """
        # Use existing selection logic
        optimal_prompt, confidence = self.select_optimal_prompt(patterns)

        print(f"\n🎯 RECOMMENDATION:")
        print(f"   Optimal prompt: {optimal_prompt}")
        print(f"   Confidence: {confidence}%")
        print(f"   Detected patterns: {', '.join(patterns.get('patterns_found', []))}")
        print(f"   → {optimal_prompt} ({confidence}%)")

        return optimal_prompt, confidence

    def route_html_content(self, html_content: str, slide_name: str = "unknown") -> Tuple[str, int]:
        """
        Route HTML content directly with slide name for better type detection
        """
        patterns = self.analyze_html_patterns(html_content, slide_name)
        optimal_prompt, confidence = self.select_optimal_prompt(patterns)

        # Use natural routing - no hardcoded overrides
        # Let the analysis determine the best prompt based on content patterns

        return optimal_prompt, confidence

    def route_html_file(self, html_file_path: str) -> Tuple[str, int]:
        """
        Route HTML file with content analysis
        """
        html_path = Path(html_file_path)
        if not html_path.exists():
            raise FileNotFoundError(f"HTML file not found: {html_file_path}")

        html_content = html_path.read_text(encoding='utf-8')
        return self.route_html_content(html_content, html_path.stem)

    def batch_analyze(self, html_directory: str = "generated_html"):
        """
        Analyze all HTML files in a directory
        """
        print(f"📁 Batch Analysis: {html_directory}")
        print("=" * 60)
        
        html_dir = Path(html_directory)
        if not html_dir.exists():
            print(f"❌ Directory not found: {html_directory}")
            return
        
        html_files = list(html_dir.glob("*.html"))
        if not html_files:
            print(f"❌ No HTML files found in: {html_directory}")
            return
        
        results = {}
        
        for html_file in sorted(html_files):
            print(f"\n📄 Analyzing: {html_file.name}")
            try:
                prompt, confidence = self.route_html_file(str(html_file))
                results[html_file.name] = {
                    'prompt': prompt,
                    'confidence': confidence
                }
                print(f"   → {prompt} ({confidence}%)")
            except Exception as e:
                print(f"   ❌ Error: {e}")
                results[html_file.name] = {'error': str(e)}
        
        # Summary
        print(f"\n📊 BATCH ANALYSIS SUMMARY:")
        print("=" * 40)
        
        prompt_counts = {}
        for file_name, result in results.items():
            if 'prompt' in result:
                prompt = result['prompt']
                prompt_counts[prompt] = prompt_counts.get(prompt, 0) + 1
                print(f"{file_name:20} → {prompt:15} ({result['confidence']}%)")
        
        print(f"\n📈 Prompt Distribution:")
        for prompt, count in sorted(prompt_counts.items()):
            print(f"   {prompt}: {count} files")

def main():
    """CLI interface for intelligent routing"""
    router = SimpleIntelligentRouter()
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python simple_router.py <html_file>           # Analyze single file")
        print("  python simple_router.py --batch [directory]   # Analyze all files")
        print("\nExamples:")
        print("  python simple_router.py slide_8_general.html")
        print("  python simple_router.py --batch generated_html")
        sys.exit(1)
    
    if sys.argv[1] == "--batch":
        directory = sys.argv[2] if len(sys.argv) > 2 else "generated_html"
        router.batch_analyze(directory)
    else:
        html_file = sys.argv[1]
        try:
            prompt, confidence = router.route_html_file(html_file)
            print(f"\n💡 To convert this file, run:")
            print(f"   python test_prompt_system.py {prompt} {Path(html_file).stem}")
        except Exception as e:
            print(f"\n❌ Routing failed: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
