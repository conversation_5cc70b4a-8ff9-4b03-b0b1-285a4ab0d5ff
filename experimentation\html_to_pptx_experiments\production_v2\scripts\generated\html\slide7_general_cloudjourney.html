<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Embrace the Cloud: Your Journey Starts Now</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');
    :root{
      --aws-orange:#FF9900;
      --aws-dark-blue:#232F3E;
      --aws-light-gray:#F2F2F2;
      --text-primary-dark:#16191F;
      --text-secondary-dark:#4A4A4A;
      --white:#FFFFFF;
      --border:#E0E1DD;
      --muted:#6B7280;
    }
    html, body { height: 100%; }
    body { margin:0; background: var(--aws-light-gray); font-family: 'Roboto', sans-serif; }
    .slide-content { /* required main content area class */
      width: 100%;
      box-sizing: border-box;
    }
    .list-item { font-size: 1.2em; line-height: 1.5; color: var(--text-secondary-dark); }
    .list-item strong { color: var(--text-primary-dark); font-weight: 700; }
    .section-title { color: var(--aws-dark-blue); }
    .badge { width: 22px; height: 22px; display: inline-flex; align-items: center; justify-content: center; border-radius: 4px; background: rgba(255,153,0,0.12); border: 1px solid rgba(255,153,0,0.4); margin-right: 10px; }
    .cta-text { font-size: 1.2em; color: var(--text-primary-dark); }
  </style>
</head>
<body class="flex items-center justify-center">

  <div class="w-[1280px] h-[720px] bg-white shadow-xl overflow-hidden flex flex-col relative">
    <!-- Header -->
    <header class="w-full border-b" style="border-color: var(--border);">
      <div class="px-20 pt-8 pb-4">
        <h1 class="text-[2.6em] font-bold leading-tight text-left" style="color: var(--text-primary-dark);">
          Embrace the Cloud: <span style="color: var(--aws-orange);">Your Journey Starts Now</span>
        </h1>
      </div>
    </header>

    <!-- Main Content -->
    <main class="slide-content flex-1 px-20 py-6">
      <div class="grid grid-cols-12 gap-10 h-full">
        <!-- Left Column: Guidance -->
        <section class="col-span-7 flex flex-col">
          <!-- Getting Started -->
          <div class="mb-5">
            <h2 class="section-title text-xl font-bold mb-3">Getting Started</h2>
            <ul class="list-disc pl-6 space-y-3">
              <li class="list-item flex">
                <span class="badge" aria-hidden="true">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                    <path d="M4 7h16M4 12h10M4 17h16" stroke="#FF9900" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                </span>
                <span>
                  <strong>AWS Free Tier:</strong> Explore AWS services with a free account. Get hands-on experience with a variety of services without incurring costs.
                </span>
              </li>
              <li class="list-item flex">
                <span class="badge" aria-hidden="true">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                    <path d="M12 3l7 4v4c0 5-3.5 9-7 10-3.5-1-7-5-7-10V7l7-4z" stroke="#FF9900" stroke-width="2" fill="none"/>
                    <path d="M9 11l3 2 3-2" stroke="#FF9900" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </span>
                <span>
                  <strong>AWS Training & Certification:</strong> Enhance your cloud skills. Validate your expertise and advance your career.
                </span>
              </li>
              <li class="list-item flex">
                <span class="badge" aria-hidden="true">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                    <path d="M7 8a4 4 0 118 0 4 4 0 01-8 0z" stroke="#FF9900" stroke-width="2"/>
                    <path d="M2 21a7 7 0 0110-6.32A7 7 0 0122 21" stroke="#FF9900" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                </span>
                <span>
                  <strong>AWS Partner Network:</strong> Connect with AWS experts. Find qualified partners to help you with your cloud journey.
                </span>
              </li>
            </ul>
          </div>

          <!-- Conclusion -->
          <div class="mb-5">
            <h2 class="section-title text-xl font-bold mb-3">Conclusion</h2>
            <ul class="list-disc pl-6 space-y-3">
              <li class="list-item">
                AWS provides a powerful and versatile platform for businesses of all sizes. Whether you're a startup or a large enterprise, AWS can help you achieve your business goals.
              </li>
              <li class="list-item">
                By leveraging AWS, you can unlock innovation, reduce costs, and achieve your business goals. Focus on your core business and let AWS handle the infrastructure.
              </li>
            </ul>
          </div>

          <!-- Call to Action -->
          <div class="mt-auto">
            <div class="rounded-md border p-4" style="border-color: var(--border); background: #FFFDFC;">
              <div class="flex items-start gap-3">
                <div class="mt-0.5">
                  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" aria-hidden="true">
                    <path d="M3 5h18v14H3V5z" stroke="#FF9900" stroke-width="2"/>
                    <path d="M3 7l9 6 9-6" stroke="#FF9900" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="flex-1">
                  <p class="cta-text font-semibold mb-1">Call to Action</p>
                  <p class="list-item mb-2">Contact us to learn more about how AWS can benefit your organization. Schedule a consultation to discuss your specific needs.</p>
                  <div class="flex flex-wrap gap-4 text-[1.05em]" style="color: var(--muted);">
                    <span class="inline-flex items-center gap-2">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true">
                        <path d="M3 7h18M3 7l9 6 9-6M3 7v10h18V7" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                      <EMAIL>
                    </span>
                    <span class="inline-flex items-center gap-2">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true">
                        <path d="M8 7V3m8 4V3M4 11h16M6 21h12a2 2 0 002-2V7a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" stroke="#6B7280" stroke-width="2" stroke-linecap="round"/>
                      </svg>
                      Book a 30-min discovery call
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Right Column: Futuristic Image -->
        <section class="col-span-5 flex flex-col">
          <div class="w-full h-[380px] rounded-md overflow-hidden border" style="border-color: var(--border);" aria-label="Futuristic cityscape representing growth and innovation with cloud technology">
            <svg viewBox="0 0 600 420" class="w-full h-full" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="sky" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stop-color="#93C5FD"/>
                  <stop offset="50%" stop-color="#3B82F6"/>
                  <stop offset="100%" stop-color="#232F3E"/>
                </linearGradient>
                <linearGradient id="accent" x1="0" y1="0" x2="1" y2="1">
                  <stop offset="0%" stop-color="#FF9900"/>
                  <stop offset="100%" stop-color="#f59e0b"/>
                </linearGradient>
              </defs>
              <!-- Sky background -->
              <rect width="600" height="420" fill="url(#sky)"/>
              <!-- Sun/Glow -->
              <circle cx="470" cy="90" r="60" fill="rgba(255,255,255,0.5)"/>
              <circle cx="470" cy="90" r="36" fill="rgba(255,255,255,0.8)"/>
              <!-- Ground -->
              <rect y="330" width="600" height="90" fill="#0f172a" opacity="0.9"/>
              <!-- Buildings -->
              <g fill="#0b1220" stroke="#374151" stroke-width="2">
                <rect x="40" y="210" width="70" height="120"/>
                <rect x="130" y="180" width="60" height="150"/>
                <rect x="210" y="150" width="80" height="180"/>
                <rect x="310" y="195" width="70" height="135"/>
                <rect x="400" y="165" width="90" height="165"/>
                <rect x="510" y="220" width="50" height="110"/>
              </g>
              <!-- Windows -->
              <g fill="#1f2937">
                <rect x="218" y="160" width="64" height="10" />
                <rect x="218" y="180" width="64" height="10" />
                <rect x="218" y="200" width="64" height="10" />
                <rect x="218" y="220" width="64" height="10" />
                <rect x="218" y="240" width="64" height="10" />
                <rect x="218" y="260" width="64" height="10" />
                <rect x="408" y="175" width="74" height="10" />
                <rect x="408" y="195" width="74" height="10" />
                <rect x="408" y="215" width="74" height="10" />
                <rect x="408" y="235" width="74" height="10" />
                <rect x="408" y="255" width="74" height="10" />
              </g>
              <!-- Orange data flow lines (innovation) -->
              <g fill="none" stroke="url(#accent)" stroke-width="3" opacity="0.9">
                <path d="M0,360 C120,320 200,340 300,330 420,320 500,300 600,310"/>
                <path d="M0,390 C120,350 240,360 360,350 470,342 540,335 600,340" opacity="0.7"/>
              </g>
              <!-- Connection nodes -->
              <g fill="#FFFFFF" stroke="#FF9900" stroke-width="2">
                <circle cx="140" cy="300" r="6"/>
                <circle cx="300" cy="330" r="7"/>
                <circle cx="470" cy="320" r="6"/>
              </g>
              <!-- Overlay label -->
              <rect x="24" y="24" width="240" height="38" rx="6" fill="rgba(0,0,0,0.25)" stroke="rgba(255,153,0,0.6)"/>
              <text x="40" y="49" fill="#FFFFFF" font-size="18" font-weight="700" font-family="Roboto, sans-serif">Future-ready with AWS</text>
            </svg>
          </div>
          <p class="mt-3 text-sm leading-snug text-left" style="color: var(--text-secondary-dark);">
            A forward-looking cityscape symbolizes growth, innovation, and the future potential unlocked by the cloud.
          </p>
        </section>
      </div>
    </main>

    <!-- Footer bar -->
    <div class="absolute bottom-0 left-0 w-full h-2" style="background: var(--aws-orange);"></div>
  </div>

</body>
</html>