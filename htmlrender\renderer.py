import os
from loguru import logger
from html2image import Html2Image
from PIL import Image, ImageFile

class HTMLRenderer():
    def __init__(self, size : tuple = (1280,720)):
        self.size = size

    def renderHTML(self, html_str : str, tmp_filename : str = "html_render.png", resize : bool = False, resize_ratio : float = 0.5) -> ImageFile.ImageFile:
        # Debug: Log HTML content preview to help track caching issues
        html_preview = html_str[:200].replace('\n', ' ').replace('\r', ' ')
        logger.info(f"🔄 Rendering HTML to {tmp_filename}: {html_preview}...")

        # Ensure HTML has proper viewport and full-size styling
        enhanced_html = self._enhance_html_for_pdf(html_str)

        # Handle both filename and full path cases
        if os.path.sep in tmp_filename:
            # Full path provided - split into directory and filename
            output_dir = os.path.dirname(tmp_filename)
            filename_only = os.path.basename(tmp_filename)
        else:
            # Just filename provided - use current directory
            output_dir = "."
            filename_only = tmp_filename

        # Ensure output directory exists and is writable (important for Cloud Run)
        os.makedirs(output_dir, exist_ok=True)

        # Create a fresh Html2Image instance for each render to avoid caching issues
        # Use custom_flags to ensure exact dimensions and no padding
        hti = Html2Image(
            size=self.size,
            output_path=output_dir,  # Set the output directory
            custom_flags=[
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--force-device-scale-factor=1',
                '--hide-scrollbars',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        )

        image_paths = hti.screenshot(html_str=enhanced_html, save_as=filename_only)
        image_path = image_paths[0]  # Get the actual path returned by Html2Image
        logger.info(f"✅ Saved rendered HTML image to temp file: {image_path}")

        pil_image = None
        try:
            # Use a context manager for Image.open to ensure the file handle is closed
            with Image.open(image_path) as img:
                img.load() # Load the image data into memory
                pil_image = img.copy() # Make a copy to decouple from the original file object
                                        # This is the most robust way to ensure the handle is released.
                                        # The 'with' block ensures 'img' is closed.

        except FileNotFoundError as e:
            logger.error(f"❌ Image file not found: {image_path}")
            logger.error(f"❌ Error: {e}")
            # Try to find the file in common locations
            possible_paths = [
                image_path,
                os.path.join(".", filename_only),
                os.path.join("/tmp", filename_only),
                os.path.join(output_dir, filename_only)
            ]

            for possible_path in possible_paths:
                if os.path.exists(possible_path):
                    logger.info(f"✅ Found image at: {possible_path}")
                    with Image.open(possible_path) as img:
                        img.load()
                        pil_image = img.copy()
                    image_path = possible_path  # Update for cleanup
                    break
            else:
                logger.error(f"❌ Could not find image file in any location")
                raise FileNotFoundError(f"Could not find rendered image: {filename_only}")

        finally:
            # Don't remove the file immediately - let the caller decide when to clean up
            # This is important for PDF generation where ReportLab needs the file to exist
            logger.info(f"Temp file created and ready for use: {image_path}")
            pass

        if resize:
            pil_image = self.resize(pil_image, ratio=resize_ratio)

        return pil_image

    def resize(self, image : ImageFile.ImageFile, ratio : float) -> ImageFile.ImageFile:

        new_width, new_height =  int(float(image.size[0])*ratio), int(float(image.size[1])*ratio)

        return image.resize((new_width, new_height), Image.Resampling.LANCZOS)

    def _enhance_html_for_pdf(self, html_str: str) -> str:
        """
        Enhance HTML to ensure it renders at full size without margins/padding for PDF export
        """
        # Check if HTML already has a viewport meta tag
        if '<meta name="viewport"' not in html_str:
            # Add viewport meta tag after <head> if it doesn't exist
            # Use device-width to allow responsive behavior instead of fixed dimensions
            if '<head>' in html_str:
                html_str = html_str.replace(
                    '<head>',
                    '<head>\n  <meta name="viewport" content="width=device-width, initial-scale=1.0">'
                )

        # Ensure html and body elements have no margins/padding and fill viewport
        # CRITICAL: Use the "html2canvasreset" approach to capture full content
        # This removes fixed dimensions AND overflow restrictions
        viewport_css = """
        <style>
        html, body {
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
            overflow: visible !important;
            width: auto !important;
            height: auto !important;
        }
        * {
            box-sizing: border-box;
        }
        </style>
        """

        # Insert the CSS right after <head> or before </head>
        if '</head>' in html_str:
            html_str = html_str.replace('</head>', viewport_css + '\n</head>')
        elif '<head>' in html_str:
            html_str = html_str.replace('<head>', '<head>' + viewport_css)
        else:
            # If no head tag, add it
            html_str = '<!DOCTYPE html><html><head>' + viewport_css + '</head><body>' + html_str + '</body></html>'

        return html_str