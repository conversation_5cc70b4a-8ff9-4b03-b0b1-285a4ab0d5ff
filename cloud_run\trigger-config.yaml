name: rmgpgab-pptx-planner-asia-northeast1-DuyquanDuc-pptx-plannercvu
description: Build and deploy to Cloud Run service pptx-planner on push to "^master$"
github:
  name: pptx-planner
  owner: DuyquanDuc
  push:
    branch: ^master$
filename: cloudbuild.yaml
includeBuildLogs: INCLUDE_BUILD_LOGS_WITH_STATUS
serviceAccount: projects/gen-lang-client-**********/serviceAccounts/<EMAIL>
substitutions:
  _TRIGGER_ID: 87b6256e-1f73-4759-a555-593752bd8bc7
  _AR_PROJECT_ID: gen-lang-client-**********
  _PLATFORM: managed
  _SERVICE_NAME: pptx-planner
  _DEPLOY_REGION: asia-northeast1
  _AR_HOSTNAME: asia-northeast1-docker.pkg.dev
  _AR_REPOSITORY: cloud-run-source-deploy
tags:
  - gcp-cloud-build-deploy-cloud-run
  - gcp-cloud-build-deploy-cloud-run-managed
  - pptx-planner
