<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>The Shift to Cloud: Why Azure?</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Segoe+UI:wght@400;700&display=swap');
    html, body { height: 100%; margin: 0; }
    body {
      background-color: #f0f2f5;
      font-family: 'Segoe UI','Roboto','Helvetica Neue',Arial,sans-serif;
    }
    .slide-content { /* main content area */ }
  </style>
</head>
<body class="flex items-center justify-center">
  <div class="w-[1280px] h-[720px] bg-white shadow-2xl overflow-hidden flex flex-col">
    <!-- Header -->
    <div class="flex items-center px-10 bg-[#0078D4] text-white" style="height:72px;">
      <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/azure-icon.svg" alt="Azure Icon" class="w-8 h-8 mr-4" />
      <div class="font-bold text-[1.2em]">Microsoft Azure</div>
    </div>

    <!-- Content -->
    <div class="slide-content flex-1 px-14 py-6 box-border">
      <!-- Title aligned top-left -->
      <h1 class="text-[2.5em] font-bold text-[#005A9E] mb-4">The Shift to Cloud: Why Azure?</h1>

      <!-- Two-column layout -->
      <div class="grid grid-cols-2 gap-8">
        <!-- Left: Narrative -->
        <div class="flex flex-col space-y-5">
          <section>
            <h2 class="text-[#0078D4] font-bold text-[1.3em] mb-2">Cloud Adoption: The Momentum</h2>
            <p class="text-[1.2em] leading-relaxed text-gray-700">
              Organizations are accelerating cloud adoption to drive digital transformation, support remote/hybrid work,
              manage explosive data growth, scale on demand, optimize costs via pay-as-you-go models, and strengthen
              business continuity with resilient, geo-distributed architectures.
            </p>
          </section>

          <section>
            <h2 class="text-[#0078D4] font-bold text-[1.3em] mb-2">Problem / Opportunity</h2>
            <ul class="list-disc pl-6 space-y-2 text-[1.2em] text-gray-700">
              <li>On‑prem constraints: high maintenance and refresh costs, limited scalability during peak loads.</li>
              <li>Security exposure: inconsistent patching, fragmented tooling, and audit/compliance gaps.</li>
              <li>Data sprawl: rapidly growing volumes without unified governance, analytics, or AI readiness.</li>
            </ul>
          </section>

          <section>
            <h2 class="text-[#0078D4] font-bold text-[1.3em] mb-2">Azure’s Value Proposition</h2>
            <ul class="space-y-3 text-[1.2em] text-gray-700">
              <li>
                <span class="font-semibold">Global Reach:</span> The broadest cloud footprint for low latency and data residency.
                Example regions relevant to global operations: East US, West Europe, Southeast Asia, Japan East, Australia East, Brazil South, UAE North.
              </li>
              <li>
                <span class="font-semibold">Hybrid Cloud Capabilities:</span> Azure Arc and Azure Stack enable seamless management and
                gradual migration across on‑prem, edge, and multi-cloud.
              </li>
              <li>
                <span class="font-semibold">Comprehensive Services:</span> Compute, storage, networking, databases, AI/ML, analytics, IoT,
                DevOps—covering modernization and innovation end‑to‑end.
              </li>
              <li>
                <span class="font-semibold">Security & Compliance:</span> Native tooling (Microsoft Defender for Cloud, Microsoft Sentinel)
                and extensive certifications (e.g., HIPAA, GDPR, ISO/IEC 27001).
              </li>
            </ul>
          </section>
        </div>

        <!-- Right: Visuals -->
        <div class="flex flex-col space-y-6">
          <!-- Bar Chart -->
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div class="flex items-baseline justify-between mb-3">
              <div class="font-semibold text-[#005A9E] text-[1.1em]">Cloud Market Share (illustrative)</div>
              <div class="text-gray-500 text-xs">Source: Industry analysts (2024)</div>
            </div>
            <div class="space-y-3">
              <!-- AWS -->
              <div class="flex items-center space-x-3">
                <div class="w-24 text-sm text-gray-700">AWS</div>
                <div class="flex-1 h-6 bg-gray-200 rounded">
                  <div class="h-6 rounded bg-gray-500" style="width:32%;"></div>
                </div>
                <div class="w-12 text-sm text-gray-700 text-right">32%</div>
              </div>
              <!-- Azure -->
              <div class="flex items-center space-x-3">
                <div class="w-24 text-sm text-gray-700">Azure</div>
                <div class="flex-1 h-6 bg-gray-200 rounded">
                  <div class="h-6 rounded" style="width:25%; background-color:#0078D4;"></div>
                </div>
                <div class="w-12 text-sm text-gray-700 text-right">25%</div>
              </div>
              <!-- Google Cloud -->
              <div class="flex items-center space-x-3">
                <div class="w-24 text-sm text-gray-700">Google Cloud</div>
                <div class="flex-1 h-6 bg-gray-200 rounded">
                  <div class="h-6 rounded" style="width:10%; background-color:#EA4335;"></div>
                </div>
                <div class="w-12 text-sm text-gray-700 text-right">10%</div>
              </div>
            </div>
            <div class="mt-3 text-xs text-gray-500">
              Note: For visualization only; exact figures vary by source and timeframe.
            </div>
          </div>

          <!-- World Map with Regions -->
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div class="font-semibold text-[#005A9E] text-[1.1em] mb-3">Azure Global Reach (selected regions)</div>
            <div class="relative w-full h-56 bg-white rounded overflow-hidden border border-gray-100">
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/8/80/World_map_-_low_resolution.svg"
                alt="World Map"
                class="w-full h-full object-contain opacity-95"
              />
              <!-- Legend colors:
                   Blue (#0078D4): Core services
                   Purple (#6B46C1): AI/ML services
                   Green (#16A34A): Compliance/sovereign-ready
              -->

              <!-- East US (Core + AI) -->
              <span class="absolute" style="top:38%; left:28%;">
                <span class="inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0078D4;"></span>
                <span class="inline-block w-2.5 h-2.5 rounded-full ml-1" style="background-color:#6B46C1;"></span>
              </span>

              <!-- West Europe (Core + Compliance) -->
              <span class="absolute" style="top:35%; left:50%;">
                <span class="inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0078D4;"></span>
                <span class="inline-block w-2.5 h-2.5 rounded-full ml-1" style="background-color:#16A34A;"></span>
              </span>

              <!-- Southeast Asia (Core) -->
              <span class="absolute" style="top:56%; left:78%;">
                <span class="inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0078D4;"></span>
              </span>

              <!-- Australia East (Core) -->
              <span class="absolute" style="top:75%; left:85%;">
                <span class="inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0078D4;"></span>
              </span>

              <!-- Japan East (Core + AI) -->
              <span class="absolute" style="top:38%; left:81%;">
                <span class="inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0078D4;"></span>
                <span class="inline-block w-2.5 h-2.5 rounded-full ml-1" style="background-color:#6B46C1;"></span>
              </span>

              <!-- Brazil South (Core) -->
              <span class="absolute" style="top:70%; left:36%;">
                <span class="inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0078D4;"></span>
              </span>

              <!-- UAE North (Core + Compliance) -->
              <span class="absolute" style="top:42%; left:60%;">
                <span class="inline-block w-2.5 h-2.5 rounded-full" style="background-color:#0078D4;"></span>
                <span class="inline-block w-2.5 h-2.5 rounded-full ml-1" style="background-color:#16A34A;"></span>
              </span>
            </div>
            <!-- Legend -->
            <div class="flex items-center space-x-6 mt-3 text-sm text-gray-700">
              <div class="flex items-center">
                <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color:#0078D4;"></span>
                <span>Core services</span>
              </div>
              <div class="flex items-center">
                <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color:#6B46C1;"></span>
                <span>AI/ML services</span>
              </div>
              <div class="flex items-center">
                <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color:#16A34A;"></span>
                <span>Compliance-ready</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>