from typing import List, Union
from PIL import Image
from llm.providers.tavily_image_search import image_tavily
import random
import io
import asyncio # Crucial for running blocking API calls in a thread
from loguru import logger
import os
from pathlib import Path

# Try to import Vertex AI components
try:
    import vertexai
    from vertexai.generative_models import GenerativeModel, Part, GenerationConfig
    from google.api_core.exceptions import ResourceExhausted, ServiceUnavailable
    VERTEX_AI_AVAILABLE = True

    # Try to import ThinkingConfig if available
    try:
        from vertexai.generative_models import ThinkingConfig
        THINKING_CONFIG_AVAILABLE = True
    except ImportError:
        THINKING_CONFIG_AVAILABLE = False
        ThinkingConfig = None

except ImportError:
    VERTEX_AI_AVAILABLE = False
    vertexai = None
    GenerativeModel = None
    Part = None
    GenerationConfig = None
    ResourceExhausted = None
    ServiceUnavailable = None
    THINKING_CONFIG_AVAILABLE = False
    ThinkingConfig = None

# Always try to import google.genai for Vertex AI
try:
    import google.genai as genai
    from google.genai import types
    from google.genai.errors import ServerError
    GEMINI_API_AVAILABLE = True
except ImportError:
    GEMINI_API_AVAILABLE = False
    genai = None
    types = None
    ServerError = None

class Vertex_Gemini_LLM:
    """
    Vertex AI-only LLM provider for main application use
    
    Key features:
    - Uses ONLY Vertex AI (no fallback to direct API)
    - Includes Tavily image search tool
    - Better error handling and retry mechanisms
    - Dedicated for main app usage where we want guaranteed Vertex AI
    """

    def __init__(self, api_key: str = None, model: str = 'gemini-2.5-pro', temperature: float = 0,
                 max_retries: int = 2, base_backoff_delay: float = 10.0,
                 project_id: str = "gen-lang-client-**********",
                 location: str = "asia-northeast1"):
        self.model_name = model
        self.temperature = temperature
        self.max_retries = max_retries
        self.base_backoff_delay = base_backoff_delay
        self.project_id = project_id
        self.location = location
        self.api_key = api_key  # Not used, but kept for compatibility

        # Only use Vertex AI - no fallback
        if not VERTEX_AI_AVAILABLE:
            raise Exception("Vertex AI is not available. Please install google-cloud-aiplatform.")
        
        if not GEMINI_API_AVAILABLE:
            raise Exception("google.genai is not available. Please install google-genai.")
        
        try:
            self._init_vertex_ai()
            logger.info("✅ Successfully initialized Vertex AI (vertex_gemini provider)")
        except Exception as e:
            logger.error(f"❌ Vertex AI initialization failed: {e}")
            raise Exception(f"Vertex AI initialization failed: {e}")

    def _init_vertex_ai(self):
        """Initialize Vertex AI provider using traditional vertexai SDK (more reliable than google.genai)"""
        # Handle service account authentication
        self._setup_authentication()

        # Validate required parameters
        if not self.project_id:
            raise ValueError("❌ GOOGLE_CLOUD_PROJECT or project_id is required for Vertex AI")

        logger.info(f"🚀 Initializing Vertex AI with traditional SDK:")
        logger.info(f"   Project ID: {self.project_id}")
        logger.info(f"   Location: {self.location}")
        logger.info(f"   Model: {self.model_name}")

        try:
            # CRITICAL: Ensure no GEMINI_API_KEY is set to prevent any fallback
            if os.getenv('GEMINI_API_KEY'):
                logger.warning("⚠️ GEMINI_API_KEY found in environment - removing to prevent fallback")
                del os.environ['GEMINI_API_KEY']

            # Initialize Vertex AI with traditional SDK (more reliable in Cloud Run)
            logger.info("🔧 Initializing vertexai...")
            vertexai.init(project=self.project_id, location=self.location)

            # Create the GenerativeModel instance
            logger.info(f"🔧 Creating GenerativeModel for {self.model_name}...")
            self.client = GenerativeModel(self.model_name)
            self.provider_type = "vertex_ai_traditional"

            logger.info(f"✅ Successfully initialized Vertex AI with traditional SDK")
            logger.info(f"✅ Model: {self.model_name}")
            logger.info(f"✅ This will use aiplatform.googleapis.com (Vertex AI)")

        except Exception as e:
            logger.error(f"❌ Failed to initialize Vertex AI: {e}")
            logger.error(f"❌ Error type: {type(e).__name__}")
            logger.error(f"❌ Error details: {str(e)}")

            # Check for common authentication issues
            if "authentication" in str(e).lower() or "credentials" in str(e).lower():
                logger.error("🔍 Authentication error detected. Common causes:")
                logger.error("   1. Service account lacks Vertex AI permissions")
                logger.error("   2. GOOGLE_APPLICATION_CREDENTIALS not set correctly")
                logger.error("   3. Default service account not configured")
            elif "project" in str(e).lower():
                logger.error(f"🔍 Project error detected. Check project ID: {self.project_id}")
            elif "api" in str(e).lower():
                logger.error("🔍 API error detected. Vertex AI API might not be enabled")

            raise Exception(f"Vertex AI initialization failed: {e}")

    def _setup_authentication(self):
        """
        Setup Google Cloud authentication with comprehensive logging and validation
        """
        # Detect environment
        is_cloud_run = bool(os.getenv('K_SERVICE'))
        has_gcp_project = bool(os.getenv('GOOGLE_CLOUD_PROJECT'))

        logger.info(f"🔍 Environment detection:")
        logger.info(f"   K_SERVICE: {os.getenv('K_SERVICE', 'Not set')}")
        logger.info(f"   GOOGLE_CLOUD_PROJECT: {os.getenv('GOOGLE_CLOUD_PROJECT', 'Not set')}")
        logger.info(f"   Is Cloud Run: {is_cloud_run}")

        if is_cloud_run or has_gcp_project:
            logger.info("🌐 PRODUCTION: Running in Cloud Run - using default service account")
            # Explicitly remove any local credentials to force default service account
            if 'GOOGLE_APPLICATION_CREDENTIALS' in os.environ:
                logger.info("🧹 Removing GOOGLE_APPLICATION_CREDENTIALS to use default service account")
                del os.environ['GOOGLE_APPLICATION_CREDENTIALS']

            # Validate that we have the required project ID
            if not self.project_id:
                self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
                logger.info(f"📋 Using project ID from environment: {self.project_id}")

            return

        # Local development - try to find service account file
        logger.info("🔧 LOCAL DEVELOPMENT: Setting up service account authentication")
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if credentials_path:
            # Convert relative path to absolute path
            if not os.path.isabs(credentials_path):
                # Find the workspace root (where the JSON file should be)
                current_dir = Path.cwd()
                workspace_root = None

                # Look for the JSON file in current directory and parent directories
                for parent in [current_dir] + list(current_dir.parents):
                    json_file = parent / Path(credentials_path).name
                    if json_file.exists():
                        workspace_root = parent
                        break

                if workspace_root:
                    absolute_path = workspace_root / Path(credentials_path).name
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(absolute_path)
                    logger.info(f"✅ Local service account resolved: {absolute_path}")
                else:
                    logger.warning(f"⚠️ Service account file not found: {credentials_path}")
                    logger.info("🌐 Falling back to default credentials")
            else:
                logger.info(f"✅ Using absolute service account path: {credentials_path}")
        else:
            logger.info("🌐 No GOOGLE_APPLICATION_CREDENTIALS - using default authentication")

    async def call(self, query: str):
        """
        Main call method for text-only queries
        """
        logger.info("🔧 Using Vertex AI provider")
        return await self._call_vertex_ai(query)

    async def call_with_images(self, query: str, images: Union[List[Image.Image], Image.Image]):
        """
        Call method for queries with images
        """
        logger.info("🔧 Using Vertex AI provider with images")
        return await self._call_vertex_ai_with_images(query, images)

    async def _call_vertex_ai(self, query: str):
        """Call using traditional Vertex AI SDK"""
        # Configure generation settings
        generation_config = GenerationConfig(temperature=self.temperature)

        # TODO: Fix tool format for traditional Vertex AI - temporarily disabled
        # tools = [image_tavily] if GEMINI_API_AVAILABLE else []

        response = await self._api_call_with_retries(
            self.client.generate_content,
            contents=query,
            generation_config=generation_config
            # tools=tools  # Temporarily disabled
        )

        # Log tool calls and extract response
        self._log_tool_calls(response)
        return self._extract_response_data(response)

    async def _call_vertex_ai_with_images(self, query: str, images: Union[List[Image.Image], Image.Image]):
        """Call Vertex AI with images"""
        # Ensure images is a list for consistent handling
        if not isinstance(images, list):
            images = [images]

        # Convert PIL images to Vertex AI Parts
        image_parts = []
        for img in images:
            img_bytes = self._image_to_byte_array(img)
            image_parts.append(Part.from_data(img_bytes, mime_type="image/jpeg"))

        # Combine text and image content for the model
        contents = [query] + image_parts

        # Configure generation settings
        generation_config = GenerationConfig(temperature=self.temperature)

        # TODO: Fix tool format for traditional Vertex AI - temporarily disabled
        # tools = [image_tavily] if GEMINI_API_AVAILABLE else []

        response = await self._api_call_with_retries(
            self.client.generate_content,
            contents=contents,
            generation_config=generation_config
            # tools=tools  # Temporarily disabled
        )

        # Log tool calls and extract response
        self._log_tool_calls(response)
        return self._extract_response_data(response)

    def _image_to_byte_array(self, image: Image.Image) -> bytes:
        """Convert PIL Image to byte array"""
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='JPEG')
        return img_byte_arr.getvalue()

    async def _api_call_with_retries(self, func, *args, **kwargs):
        """
        Helper method to perform an API call with retries and exponential backoff.
        Handles Vertex AI errors and detects wrong API usage.
        """
        for attempt in range(self.max_retries + 1):
            try:
                # Execute the blocking call in a separate thread
                response = await asyncio.to_thread(func, *args, **kwargs)
                return response

            except Exception as e:
                error_str = str(e).lower()

                # CRITICAL: Detect if we're accidentally using direct Gemini API
                if "generativelanguage.googleapis.com" in error_str:
                    logger.error("🚨 CRITICAL ERROR: Using direct Gemini API instead of Vertex AI!")
                    logger.error("🚨 This means the client was not properly initialized with vertexai=True")
                    logger.error(f"🚨 Error: {e}")
                    raise Exception(f"WRONG API DETECTED: Using direct Gemini API instead of Vertex AI: {e}")

                # Check for API key errors (should not happen with Vertex AI)
                if "api key" in error_str and "expired" in error_str:
                    logger.error("🚨 CRITICAL ERROR: API key error in Vertex AI provider!")
                    logger.error("🚨 This should not happen - Vertex AI uses service account authentication")
                    logger.error(f"🚨 Error: {e}")
                    raise Exception(f"API KEY ERROR IN VERTEX AI: {e}")

                # Handle Vertex AI errors
                if VERTEX_AI_AVAILABLE and (ResourceExhausted and isinstance(e, (ResourceExhausted, ServiceUnavailable))):
                    if attempt < self.max_retries:
                        delay = min(self.base_backoff_delay * (2 ** attempt), 60)  # Cap at 60 seconds
                        jitter = random.uniform(0, delay * 0.1)
                        sleep_time = delay + jitter
                        logger.error(f"[Vertex AI Error] {e} : Retrying in {sleep_time:.2f} seconds (attempt {attempt + 1}/{self.max_retries}).")
                        await asyncio.sleep(sleep_time)
                    else:
                        logger.error(f"[Vertex AI Error] {e} : Retries exceeded.")
                        raise
                # Handle google.genai ServerError
                elif GEMINI_API_AVAILABLE and ServerError and isinstance(e, ServerError):
                    if e.code in [503, 429] and attempt < self.max_retries:
                        delay = min(self.base_backoff_delay * (2 ** attempt), 60)
                        jitter = random.uniform(0, delay * 0.1)
                        sleep_time = delay + jitter
                        logger.error(f"[GenAI Error] {e} : Retrying in {sleep_time:.2f} seconds (attempt {attempt + 1}/{self.max_retries}).")
                        await asyncio.sleep(sleep_time)
                    else:
                        logger.error(f"[GenAI Error] {e} : Retries exceeded.")
                        raise
                else:
                    # For any other error, log details and raise
                    logger.error(f"[Vertex AI Error] {e}")
                    logger.error(f"[Error Type] {type(e).__name__}")
                    raise

    def _log_tool_calls(self, response):
        """Log any tool calls made during the response"""
        if hasattr(response, 'candidates') and response.candidates:
            for candidate in response.candidates:
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    for part in candidate.content.parts:
                        if hasattr(part, 'function_call') and part.function_call:
                            logger.info(f"🔧 Tool called: {part.function_call.name}")

    def _extract_response_data(self, response):
        """Extract response data and token counts"""
        # Extract the text content
        text_content = ""
        if hasattr(response, 'candidates') and response.candidates:
            candidate = response.candidates[0]
            if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                for part in candidate.content.parts:
                    if hasattr(part, 'text'):
                        text_content += part.text

        # Extract token counts
        input_token_count = 0
        output_token_count = 0

        if hasattr(response, 'usage_metadata'):
            if hasattr(response.usage_metadata, 'prompt_token_count'):
                input_token_count = response.usage_metadata.prompt_token_count
            if hasattr(response.usage_metadata, 'candidates_token_count'):
                output_token_count = response.usage_metadata.candidates_token_count

        return {
            'text': text_content,
            'input_token_count': input_token_count,
            'output_token_count': output_token_count
        }
