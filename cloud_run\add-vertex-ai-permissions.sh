#!/bin/bash

# Add Vertex AI permissions to existing service account
# Run this script to enable Vertex AI for your existing service account

PROJECT_ID="gen-lang-client-**********"
SERVICE_ACCOUNT_EMAIL="<EMAIL>"

echo "🤖 Adding Vertex AI permissions to existing service account..."

# Set the project
gcloud config set project $PROJECT_ID

# Add Vertex AI User role
echo "🔧 Adding Vertex AI User role..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/aiplatform.user"

# Add ML Developer role (optional but recommended)
echo "🧠 Adding ML Developer role..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/ml.developer"

# Enable Vertex AI API if not already enabled
echo "🚀 Enabling Vertex AI API..."
gcloud services enable aiplatform.googleapis.com --project=$PROJECT_ID

echo "✅ Vertex AI permissions added successfully!"
echo ""
echo "📋 Summary of permissions added:"
echo "  - roles/aiplatform.user (for Vertex AI model access)"
echo "  - roles/ml.developer (for ML operations)"
echo ""
echo "🔧 Your service account now has:"
echo "  - Firebase Admin (existing)"
echo "  - Storage Object Admin (existing)"
echo "  - Storage Object Creator (existing)"
echo "  - Vertex AI User (new)"
echo "  - ML Developer (new)"
echo ""
echo "🚀 Ready to deploy with Vertex AI support!"
echo ""
echo "💡 Next steps:"
echo "1. Deploy your updated code with: gcloud builds submit"
echo "2. Test Vertex AI functionality in production"
echo "3. Monitor logs for any authentication issues"
