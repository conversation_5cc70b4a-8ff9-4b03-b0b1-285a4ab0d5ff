# Custom IAM Role with Minimal Permissions for PPTX Planner
# Use this to create a custom role with only the required permissions

title: "PPTX Planner Custom Role"
description: "Minimal permissions for PPTX Planner service account"
stage: "GA"
includedPermissions:
  # Firebase permissions
  - firebase.projects.get
  
  # Storage permissions for bucket operations
  - storage.buckets.get
  
  # Storage permissions for object operations
  - storage.objects.create
  - storage.objects.delete
  - storage.objects.get
  - storage.objects.list
  
  # Permission for generating signed URLs
  - iam.serviceAccounts.signBlob

# To create this custom role, run:
# gcloud iam roles create pptxPlannerRole --project=gen-lang-client-********** --file=custom-role-permissions.yaml
#
# Then assign it to your service account:
# gcloud projects add-iam-policy-binding gen-lang-client-********** \
#   --member="serviceAccount:<EMAIL>" \
#   --role="projects/gen-lang-client-**********/roles/pptxPlannerRole"
