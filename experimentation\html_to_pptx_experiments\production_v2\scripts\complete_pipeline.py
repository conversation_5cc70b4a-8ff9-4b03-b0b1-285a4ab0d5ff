#!/usr/bin/env python3
"""
Complete Pipeline V2 - Production-Ready Multi-Slide Generation

This script orchestrates the complete pipeline using all production_v2 components:
1. HTML → Individual JavaScript (auto_convert.py)
2. Individual JS → Consolidated JS (js_merger.py) 
3. Consolidated JS → PowerPoint (Node.js execution)

All dependencies are now properly organized in production_v2.
"""

import sys
import asyncio
import subprocess
import argparse
from pathlib import Path


class CompletePipelineV2:
    """
    Complete pipeline using production_v2 components
    """
    
    def __init__(self, base_dir: Path = None):
        if base_dir is None:
            base_dir = Path(__file__).parent.parent
        
        self.base_dir = base_dir
        self.scripts_dir = base_dir / "scripts"
        self.output_dir = base_dir / "generated"
        
        print(f"🚀 Complete Pipeline V2 initialized")
        print(f"📁 Base directory: {base_dir}")
        print(f"📁 Scripts directory: {self.scripts_dir}")
        print(f"📁 Output directory: {self.output_dir}")
    
    def run_auto_convert(self, html_dir: Path, force_prompt: str = None) -> bool:
        """Run auto_convert.py to generate individual JavaScript files"""
        
        print(f"\n🔄 Step 1: Running auto_convert.py")
        print(f"📁 HTML directory: {html_dir}")
        if force_prompt:
            print(f"🎯 Forced prompt: {force_prompt}")
        
        try:
            # Build command
            cmd = [
                sys.executable, 
                str(self.scripts_dir / "auto_convert.py"),
                "--batch",
                str(html_dir)
            ]
            
            if force_prompt:
                cmd.append(force_prompt)
            
            print(f"🔄 Executing: {' '.join(cmd)}")
            
            # Run auto_convert.py
            result = subprocess.run(
                cmd,
                cwd=self.scripts_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes
            )
            
            if result.returncode == 0:
                print(f"✅ auto_convert.py completed successfully")
                print(f"📄 Output preview:")
                # Show last few lines of output
                output_lines = result.stdout.strip().split('\n')
                for line in output_lines[-10:]:
                    if line.strip():
                        print(f"   {line}")
                return True
            else:
                print(f"❌ auto_convert.py failed with exit code {result.returncode}")
                print(f"❌ STDERR: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"❌ auto_convert.py timed out after 5 minutes")
            return False
        except Exception as e:
            print(f"❌ Error running auto_convert.py: {e}")
            return False
    
    def run_js_merger(self, js_dir: Path, output_name: str) -> tuple[bool, Path | None]:
        """Run js_merger.py to create consolidated JavaScript"""
        
        print(f"\n🔄 Step 2: Running js_merger.py")
        print(f"📁 JavaScript directory: {js_dir}")
        print(f"📄 Output name: {output_name}")
        
        try:
            # Build command
            cmd = [
                sys.executable,
                str(self.scripts_dir / "js_merger.py"),
                "--js-dir", str(js_dir),
                "--output-name", output_name
            ]
            
            print(f"🔄 Executing: {' '.join(cmd)}")
            
            # Run js_merger.py
            result = subprocess.run(
                cmd,
                cwd=self.scripts_dir,
                capture_output=True,
                text=True,
                timeout=120  # 2 minutes
            )
            
            if result.returncode == 0:
                print(f"✅ js_merger.py completed successfully")
                print(f"📄 Output preview:")
                # Show last few lines of output
                output_lines = result.stdout.strip().split('\n')
                for line in output_lines[-5:]:
                    if line.strip():
                        print(f"   {line}")
                
                # Find the generated PowerPoint file
                # js_merger.py saves to scripts/generated/presentations, not base/generated/presentations
                presentations_dir = self.scripts_dir / "generated" / "presentations"
                pptx_file = presentations_dir / f"{output_name}.pptx"

                if pptx_file.exists():
                    return True, pptx_file
                else:
                    print(f"⚠️ PowerPoint file not found at expected location: {pptx_file}")
                    # Fallback: search for any .pptx files with the output name
                    fallback_files = list(presentations_dir.glob(f"*{output_name}*.pptx"))
                    if fallback_files:
                        pptx_file = fallback_files[0]
                        print(f"✅ Found PowerPoint file at: {pptx_file}")
                        return True, pptx_file
                    return True, None
            else:
                print(f"❌ js_merger.py failed with exit code {result.returncode}")
                print(f"❌ STDERR: {result.stderr}")
                return False, None
                
        except subprocess.TimeoutExpired:
            print(f"❌ js_merger.py timed out after 2 minutes")
            return False, None
        except Exception as e:
            print(f"❌ Error running js_merger.py: {e}")
            return False, None
    
    def run_complete_pipeline(self, html_dir: Path, output_name: str = "complete_pipeline_output", 
                            force_prompt: str = None) -> dict:
        """Run the complete pipeline from HTML to PowerPoint"""
        
        print(f"🚀 Starting Complete Pipeline V2")
        print(f"📁 HTML directory: {html_dir}")
        print(f"📄 Output name: {output_name}")
        print("=" * 60)
        
        try:
            # Step 1: Run auto_convert.py
            auto_convert_success = self.run_auto_convert(html_dir, force_prompt)
            
            if not auto_convert_success:
                return {
                    'success': False,
                    'error': 'auto_convert.py failed',
                    'step_failed': 'auto_convert',
                    'final_presentation_path': None
                }
            
            # Step 2: Find generated JavaScript directory
            # auto_convert.py now saves to scripts/generated/javascript/individual
            js_dir = self.scripts_dir / "generated" / "javascript" / "individual"
            
            if not js_dir.exists():
                return {
                    'success': False,
                    'error': f'JavaScript directory not found: {js_dir}',
                    'step_failed': 'js_directory_check',
                    'final_presentation_path': None
                }
            
            # Step 3: Run js_merger.py
            merger_success, pptx_file = self.run_js_merger(js_dir, output_name)
            
            if not merger_success:
                return {
                    'success': False,
                    'error': 'js_merger.py failed',
                    'step_failed': 'js_merger',
                    'final_presentation_path': None
                }
            
            # Success!
            file_size = pptx_file.stat().st_size if pptx_file and pptx_file.exists() else 0
            
            print(f"\n🎉 Complete Pipeline V2 SUCCESS!")
            print(f"✅ All steps completed successfully")
            if pptx_file:
                print(f"📄 Final presentation: {pptx_file}")
                print(f"📊 File size: {file_size:,} bytes")
            
            return {
                'success': True,
                'final_presentation_path': str(pptx_file) if pptx_file else None,
                'file_size': file_size,
                'steps_completed': ['auto_convert', 'js_merger'],
                'error': None
            }
            
        except Exception as e:
            print(f"\n❌ Complete Pipeline V2 failed with exception: {e}")
            return {
                'success': False,
                'error': str(e),
                'step_failed': 'exception',
                'final_presentation_path': None
            }


def main():
    """CLI interface for complete pipeline"""
    parser = argparse.ArgumentParser(description='Complete Pipeline V2 - Production Multi-Slide Generation')
    parser.add_argument('--html-dir', type=str, required=True,
                       help='Directory containing HTML files')
    parser.add_argument('--output-name', type=str, default='complete_pipeline_output',
                       help='Name for the output presentation')
    parser.add_argument('--force-prompt', type=str, 
                       help='Force specific prompt (e.g., ultra_safe, balanced)')
    parser.add_argument('--base-dir', type=str, help='Base directory for pipeline')
    
    args = parser.parse_args()
    
    # Initialize pipeline
    base_dir = Path(args.base_dir) if args.base_dir else None
    pipeline = CompletePipelineV2(base_dir)
    
    # Run complete pipeline
    html_dir = Path(args.html_dir)
    result = pipeline.run_complete_pipeline(
        html_dir,
        args.output_name,
        args.force_prompt
    )
    
    # Print final results
    print(f"\n🎯 Final Results:")
    for key, value in result.items():
        print(f"   {key}: {value}")
    
    # Exit with appropriate code
    exit_code = 0 if result['success'] else 1
    exit(exit_code)


if __name__ == "__main__":
    main()
