# 🚀 PPTX-Planner Project Setup & Usage Guidelines

## 📋 **CRITICAL SETUP REQUIREMENTS**

### **⚠️ MANDATORY: Virtual Environment Activation**

**ALWAYS activate the virtual environment before running ANY Python commands in this project:**

```powershell
# Navigate to project root
cd C:\Users\<USER>\source\pptx-planner

# Activate virtual environment (REQUIRED)
& C:/Users/<USER>/source/pptx-planner/.venv/Scripts/Activate.ps1

# Verify activation (should show (.venv) in prompt)
# (.venv) PS C:\Users\<USER>\source\pptx-planner>
```

**🚨 FAILURE TO ACTIVATE VENV WILL CAUSE:**
- Import errors (`ModuleNotFoundError`)
- Missing dependencies
- System-wide package conflicts
- Broken functionality

---

## 🏗️ **PROJECT ARCHITECTURE**

### **Backend (FastAPI)**
- **Main File**: `app.py` (FastAPI application)
- **Port**: 8000 (default)
- **Dependencies**: Poetry-managed Python packages
- **Environment**: Requires `.venv` activation

### **Frontend (Static HTML/JS)**
- **Location**: `src/` directory
- **Entry Point**: `src/index.html`
- **Technology**: Vanilla JavaScript + Tailwind CSS
- **Serving**: Static files served by FastAPI

### **Key Components**
- **LLM Integration**: `llm/llmwrapper.py`
- **PowerPoint Generation**: `pptx_generation/`
- **HTML Rendering**: `htmlrender/renderer.py`
- **Prompts**: `prompt/` directory
- **Experiments**: `experimentation/` directory

---

## 🚀 **STARTUP PROCEDURES**

### **1. Backend Server (FastAPI)**

```powershell
# Step 1: Navigate to project root
cd C:\Users\<USER>\source\pptx-planner

# Step 2: MANDATORY - Activate virtual environment
& C:/Users/<USER>/source/pptx-planner/.venv/Scripts/Activate.ps1

# Step 3: Start FastAPI server
python -m uvicorn app:app --reload --host 127.0.0.1 --port 8000

# Expected output:
# INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
# INFO:     Started reloader process
```

### **2. Frontend Access**

```
# Once backend is running, access frontend at:
http://127.0.0.1:8000/

# Main pages:
http://127.0.0.1:8000/                    # Landing page
http://127.0.0.1:8000/input_page.html     # Input form
http://127.0.0.1:8000/outline.html        # Outline editor
http://127.0.0.1:8000/output_page.html    # Generated slides
```

### **3. API Endpoints**

```
# Core API endpoints:
POST /plan                    # Generate outline from user input
POST /process-outline         # Generate slides from outline
POST /generate-slides         # Direct slide generation
POST /fix-slide              # Fix individual slides
POST /export-powerpoint      # Export to PowerPoint
POST /export-pdf             # Export to PDF
WS   /ws/generate-presentation # WebSocket for real-time updates
```

---

## 🔧 **DEVELOPMENT WORKFLOWS**

### **HTML-to-PowerPoint Experiments**

```powershell
# Navigate to experiments directory
cd C:\Users\<USER>\source\pptx-planner\experimentation\html_to_pptx_experiments

# MANDATORY - Activate venv first
& C:/Users/<USER>/source/pptx-planner/.venv/Scripts/Activate.ps1

# Production workflow
python production\scripts\simple_router.py --batch generated\html
python production\scripts\consolidated_converter.py generated\html final.pptx

# Development testing
python development\testing\test_prompt_system.py ultra_safe slide_8_general
```

### **Main Application Development**

```powershell
# From project root with venv activated
cd C:\Users\<USER>\source\pptx-planner
& C:/Users/<USER>/source/pptx-planner/.venv/Scripts/Activate.ps1

# Run tests
python -m pytest test/

# Check specific modules
python -c "from llm.llmwrapper import LLM; print('LLM module working')"
python -c "from pptx_generation.planner import Planner; print('Planner working')"
```

---

## 📦 **DEPENDENCY MANAGEMENT**

### **Python Dependencies (Poetry)**

```powershell
# Install dependencies (with venv activated)
poetry install

# Add new dependency
poetry add package-name

# Update dependencies
poetry update

# Check dependency status
poetry show
```

### **Node.js Dependencies**

```powershell
# Install Node.js packages (for PptxGenJS)
npm install

# Check installed packages
npm list
```

---

## 🌍 **ENVIRONMENT CONFIGURATION**

### **Required Environment File**
- **Location**: `local.env` (in project root)
- **Contains**: API keys, configuration settings
- **Format**:
```env
GEMINI_API_KEY=your_gemini_key_here
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
TAVILY_API_KEY=your_tavily_key_here
```

### **Environment Loading**
- Automatically loaded by `app.py` using `python-dotenv`
- Available to all modules via `os.getenv()`

---

## 🚨 **COMMON ISSUES & SOLUTIONS**

### **1. Import Errors**
```
❌ ModuleNotFoundError: No module named 'llm'
✅ SOLUTION: Activate virtual environment first
& C:/Users/<USER>/source/pptx-planner/.venv/Scripts/Activate.ps1
```

### **2. Server Won't Start**
```
❌ Port 8000 already in use
✅ SOLUTION: Kill existing process or use different port
python -m uvicorn app:app --reload --host 127.0.0.1 --port 8001
```

### **3. Frontend Not Loading**
```
❌ 404 errors for static files
✅ SOLUTION: Ensure FastAPI server is running and serving static files
```

### **4. LLM API Errors**
```
❌ API key not found
✅ SOLUTION: Check local.env file exists and contains valid keys
```

---

## 📁 **DIRECTORY STRUCTURE GUIDE**

```
pptx-planner/
├── 🚀 app.py                          # Main FastAPI application
├── 📦 pyproject.toml                  # Poetry dependencies
├── 🔐 local.env                       # Environment variables
├── 📁 src/                            # Frontend files
│   ├── index.html                     # Landing page
│   ├── input_page.html                # User input form
│   ├── outline.html                   # Outline editor
│   └── output_page.html               # Generated slides
├── 📁 llm/                            # LLM integration
├── 📁 pptx_generation/                # PowerPoint generation
├── 📁 htmlrender/                     # HTML rendering
├── 📁 prompt/                         # Prompt templates
├── 📁 experimentation/                # Experiments & prototypes
│   └── html_to_pptx_experiments/      # HTML-to-PowerPoint system
│       ├── production/                # Production-ready scripts
│       ├── development/               # Development tools
│       ├── archive/                   # Legacy components
│       └── generated/                 # Generated content
└── 📁 temp_presentations/             # Temporary outputs
```

---

## 🎯 **USAGE PATTERNS**

### **Standard User Workflow**
1. **Start Backend**: `uvicorn app:app --reload` (with venv)
2. **Open Frontend**: `http://127.0.0.1:8000/`
3. **Input Request**: Enter presentation requirements
4. **Review Outline**: Edit generated outline if needed
5. **Generate Slides**: Process outline into HTML slides
6. **Export**: Download PowerPoint or PDF

### **Developer Workflow**
1. **Activate Environment**: Always activate `.venv` first
2. **Start Server**: Run FastAPI with reload for development
3. **Test Changes**: Use browser dev tools and API testing
4. **Experiment**: Use `experimentation/` directory for prototypes
5. **Deploy**: Use production scripts for final outputs

---

## 📞 **SUPPORT & TROUBLESHOOTING**

### **Quick Diagnostics**
```powershell
# Check virtual environment
echo $env:VIRTUAL_ENV  # Should show .venv path

# Check Python path
python -c "import sys; print(sys.path[0])"

# Test core imports
python -c "from app import app; print('FastAPI app loaded')"

# Check server status
curl http://127.0.0.1:8000/
```

### **Emergency Reset**
```powershell
# Kill all Python processes
taskkill /f /im python.exe

# Restart with clean environment
& C:/Users/<USER>/source/pptx-planner/.venv/Scripts/Activate.ps1
python -m uvicorn app:app --reload --host 127.0.0.1 --port 8000
```

---

## ⚡ **PERFORMANCE TIPS**

- **Always use virtual environment** for consistent dependencies
- **Use `--reload` flag** during development for auto-restart
- **Monitor memory usage** during large presentation generation
- **Clean temp files** regularly from `temp_presentations/`
- **Use WebSocket endpoint** for real-time slide generation updates

---

**🎉 READY FOR DEVELOPMENT!**

Remember: **ALWAYS ACTIVATE VIRTUAL ENVIRONMENT FIRST** - this is the #1 cause of issues in this project!
