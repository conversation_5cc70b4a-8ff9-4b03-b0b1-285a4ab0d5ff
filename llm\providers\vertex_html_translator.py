import vertexai
from vertexai.generative_models import GenerativeModel, Part, GenerationConfig
from typing import List, Union
from PIL import Image
import random
from google.api_core.exceptions import ResourceExhausted, ServiceUnavailable
import io
import asyncio
from loguru import logger
import os
from pathlib import Path

# Try to import ThinkingConfig if available
try:
    from vertexai.generative_models import ThinkingConfig
    THINKING_CONFIG_AVAILABLE = True
except ImportError:
    THINKING_CONFIG_AVAILABLE = False
    ThinkingConfig = None

class Vertex_HTML_Translator_LLM:
    """
    Vertex AI LLM provider for HTML-to-PowerPoint translation.
    
    Key features:
    - Uses Vertex AI instead of Gemini API directly
    - NO TOOLS (no image_tavily or other tools)
    - Optimized for pure text generation
    - Focused on HTML translation tasks
    - Better reliability than direct Gemini API
    """
    
    def __init__(self, project_id: str = "gen-lang-client-**********",
                 location: str = "asia-northeast1",
                 model: str = 'gemini-2.5-pro',
                 temperature: float = 0.2,
                 max_retries: int = 2, 
                 base_backoff_delay: float = 10.0):
        
        self.project_id = project_id
        self.location = location
        self.model_name = model
        self.temperature = temperature
        self.max_retries = max_retries
        self.base_backoff_delay = base_backoff_delay
        self.max_backoff_delay = 45.0
        
        # Handle service account authentication (works with both JSON file and Cloud Run service account)
        self._setup_authentication()

        # Initialize Vertex AI
        vertexai.init(project=project_id, location=location)
        
        # Initialize the model
        self.model = GenerativeModel(model)
        
        # Set up generation config and thinking config
        self.generation_config = GenerationConfig(
            temperature=temperature
        )

        # Set up thinking config with budget if available
        if THINKING_CONFIG_AVAILABLE and ThinkingConfig:
            try:
                self.thinking_config = ThinkingConfig(thinking_budget=8492)
                logger.info("✅ Thinking budget enabled: 3000 tokens")
            except (TypeError, ValueError, AttributeError) as e:
                self.thinking_config = None
                logger.info("⚠️ Thinking budget not supported in this configuration")
        else:
            self.thinking_config = None
            logger.info("⚠️ ThinkingConfig not available in this Vertex AI version")
        
        logger.info(f"Initialized Vertex AI HTML Translator with model: {model}")

    def _setup_authentication(self):
        """
        Setup Google Cloud authentication by resolving the service account path
        """
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if credentials_path:
            # Convert relative path to absolute path
            if not os.path.isabs(credentials_path):
                # Find the workspace root (where the JSON file should be)
                current_dir = Path.cwd()
                workspace_root = None

                # Look for the JSON file in current directory and parent directories
                for parent in [current_dir] + list(current_dir.parents):
                    json_file = parent / Path(credentials_path).name
                    if json_file.exists():
                        workspace_root = parent
                        break

                if workspace_root:
                    absolute_path = workspace_root / Path(credentials_path).name
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(absolute_path)
                    logger.info(f"Resolved service account path: {absolute_path}")
                else:
                    logger.warning(f"Service account file not found: {credentials_path}")
            else:
                logger.info(f"Using absolute service account path: {credentials_path}")
        else:
            logger.info("No GOOGLE_APPLICATION_CREDENTIALS found, using Cloud Run default service account authentication")

    async def _api_call_with_retries(self, func, *args, **kwargs):
        """
        Helper method to perform an API call with retries and exponential backoff.
        Catches ServiceUnavailable (503) and ResourceExhausted (429) errors.
        """
        for attempt in range(self.max_retries + 1):
            try:
                # Execute the blocking call in a separate thread
                response = await asyncio.to_thread(func, *args, **kwargs)
                return response
            
            except (ResourceExhausted, ServiceUnavailable) as e:
                if attempt < self.max_retries:
                    delay = min(self.base_backoff_delay * (2 ** attempt), self.max_backoff_delay)
                    jitter = random.uniform(0, delay * 0.1)
                    sleep_time = delay + jitter
                    logger.error(f"[Error] {e} : Retrying in {sleep_time:.2f} seconds (attempt {attempt + 1}/{self.max_retries}).")
                    await asyncio.sleep(sleep_time)
                else:
                    logger.error(f"[Error] {e} : Retries exceeded.")
                    raise
            except Exception as e:
                logger.error(f"[Error] Unexpected error: {e}")
                raise

    async def call(self, query: str):
        """
        Pure text generation call - NO TOOLS for HTML translation tasks
        """
        try:
            # Prepare generation arguments
            generation_args = {
                "generation_config": self.generation_config
            }

            # Add thinking config if available
            if self.thinking_config:
                generation_args["thinking_config"] = self.thinking_config

            response = await self._api_call_with_retries(
                self.model.generate_content,
                query,
                **generation_args
            )

            # Extract token counts if available
            input_token_count = None
            output_token_count = None
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                input_token_count = getattr(response.usage_metadata, 'prompt_token_count', None)
                output_token_count = getattr(response.usage_metadata, 'candidates_token_count', None)

            return {
                'text': response.text,
                'input_token_count': input_token_count,
                'output_token_count': output_token_count
            }
            
        except Exception as e:
            logger.error(f"Error in Vertex AI call: {e}")
            raise

    async def call_with_images(self, query: str, images: List[Union[str, Image.Image]]):
        """
        Call with images support (though HTML translator typically won't need this)
        """
        try:
            # Process images
            processed_parts = [query]
            
            for img in images:
                if isinstance(img, str):
                    # Assume it's a file path
                    with open(img, 'rb') as f:
                        image_data = f.read()
                    processed_parts.append(Part.from_data(image_data, mime_type="image/png"))
                elif isinstance(img, Image.Image):
                    # Convert PIL Image to bytes
                    img_byte_arr = io.BytesIO()
                    img.save(img_byte_arr, format='PNG')
                    image_data = img_byte_arr.getvalue()
                    processed_parts.append(Part.from_data(image_data, mime_type="image/png"))
                else:
                    raise ValueError(f"Unsupported image type: {type(img)}")

            # Prepare generation arguments
            generation_args = {
                "generation_config": self.generation_config
            }

            # Add thinking config if available
            if self.thinking_config:
                generation_args["thinking_config"] = self.thinking_config

            response = await self._api_call_with_retries(
                self.model.generate_content,
                processed_parts,
                **generation_args
            )

            # Extract token counts if available
            input_token_count = None
            output_token_count = None
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                input_token_count = getattr(response.usage_metadata, 'prompt_token_count', None)
                output_token_count = getattr(response.usage_metadata, 'candidates_token_count', None)

            return {
                'text': response.text,
                'input_token_count': input_token_count,
                'output_token_count': output_token_count
            }
            
        except Exception as e:
            logger.error(f"Error in Vertex AI call with images: {e}")
            raise

    def get_model_info(self):
        """
        Return information about this LLM provider
        """
        return {
            "provider": "vertex_html_translator",
            "model": self.model_name,
            "temperature": self.temperature,
            "tools": "none",
            "purpose": "HTML-to-PowerPoint translation",
            "project_id": self.project_id,
            "location": self.location
        }
