import os
import json
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from google.cloud import storage
from google.cloud.exceptions import NotFound
from loguru import logger
import tempfile
import gzip
import io

class CloudStorageService:
    def __init__(self, bucket_name: str = None):
        """Initialize Google Cloud Storage service"""
        self.client = storage.Client()
        self.bucket_name = bucket_name or os.getenv('GCS_BUCKET_NAME', 'pptx-planner-storage')
        self.bucket = self.client.bucket(self.bucket_name)
        logger.info(f"CloudStorageService initialized with bucket: {self.bucket_name}")

    def _validate_user_access(self, user_id: str, file_path: str) -> bool:
        """Ensure user can only access their own files"""
        allowed_prefixes = [
            f"users/{user_id}/",
            f"temp/{user_id}_"  # Allow temp files with user ID prefix
        ]
        return any(file_path.startswith(prefix) for prefix in allowed_prefixes)

    def _compress_content(self, content: str) -> bytes:
        """Compress string content using gzip"""
        return gzip.compress(content.encode('utf-8'))

    def _decompress_content(self, compressed_data: bytes) -> str:
        """Decompress gzip content back to string"""
        return gzip.decompress(compressed_data).decode('utf-8')

    def save_presentation(self, user_id: str, presentation_data: Dict[str, Any]) -> str:
        """
        Save complete presentation with metadata
        Returns: presentation_id
        """
        presentation_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        
        # Create metadata
        metadata = {
            "presentation_id": presentation_id,
            "user_id": user_id,
            "created_at": timestamp,
            "title": presentation_data.get("title", "Untitled Presentation"),
            "slide_count": len(presentation_data.get("slides", [])),
            "user_input": presentation_data.get("user_input", ""),
            "outline": presentation_data.get("outline", "")
        }
        
        # Save metadata
        metadata_path = f"users/{user_id}/presentations/{presentation_id}/metadata.json"
        self._upload_json(metadata_path, metadata)
        
        # Save individual slides
        slides = presentation_data.get("slides", [])
        for i, slide in enumerate(slides):
            slide_path = f"users/{user_id}/presentations/{presentation_id}/slides/slide_{i+1}.html"
            html_content = slide.get("html_content", "")

            # Use BytesIO to ensure proper Content-Type handling
            from io import BytesIO
            html_bytes = html_content.encode('utf-8')
            html_file = BytesIO(html_bytes)

            blob = self.bucket.blob(slide_path)
            blob.upload_from_file(html_file, content_type="text/html")
            logger.debug(f"Successfully uploaded slide {i+1} to {slide_path}")
        
        logger.info(f"Saved presentation {presentation_id} for user {user_id} with {len(slides)} slides")
        return presentation_id

    def save_export_file(self, user_id: str, presentation_id: str, file_data: bytes, 
                        file_type: str, filename: str = None) -> str:
        """
        Save PPTX/PDF export file
        Returns: download URL
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            extension = "pptx" if file_type == "powerpoint" else "pdf"
            filename = f"presentation_{timestamp}.{extension}"
        
        file_path = f"users/{user_id}/presentations/{presentation_id}/exports/{filename}"
        
        content_type = {
            "powerpoint": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "pdf": "application/pdf"
        }.get(file_type, "application/octet-stream")
        
        self._upload_bytes(file_path, file_data, content_type=content_type)
        
        # Generate signed URL for download
        download_url = self.generate_download_url(user_id, file_path)
        
        logger.info(f"Saved {file_type} export for presentation {presentation_id}")
        return download_url

    def save_temp_file(self, user_id: str, file_data: bytes, filename: str, 
                      session_id: str = None) -> str:
        """
        Save temporary processing file
        Returns: file path
        """
        if not session_id:
            session_id = str(uuid.uuid4())
        
        file_path = f"temp/{user_id}_{session_id}/{filename}"
        self._upload_bytes(file_path, file_data)
        
        logger.info(f"Saved temp file: {file_path}")
        return file_path

    def get_user_presentations(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get list of user's presentations for dashboard
        """
        presentations = []
        prefix = f"users/{user_id}/presentations/"
        
        try:
            # List all presentation directories
            blobs = self.client.list_blobs(self.bucket, prefix=prefix, delimiter="/")
            
            for page in blobs.pages:
                for prefix_item in page.prefixes:
                    # Extract presentation ID from path
                    presentation_id = prefix_item.split("/")[-2]
                    
                    # Get metadata
                    metadata_path = f"{prefix_item}metadata.json"
                    try:
                        metadata = self._download_json(metadata_path)
                        presentations.append(metadata)
                    except NotFound:
                        logger.warning(f"Metadata not found for presentation: {presentation_id}")
                        continue
                    
                    if len(presentations) >= limit:
                        break
                        
        except Exception as e:
            logger.error(f"Error fetching presentations for user {user_id}: {e}")
        
        # Sort by creation date (newest first)
        presentations.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        return presentations

    def generate_download_url(self, user_id: str, file_path: str, 
                            expiration_minutes: int = 60) -> str:
        """Generate signed URL for secure downloads"""
        if not self._validate_user_access(user_id, file_path):
            raise PermissionError(f"Access denied to path: {file_path}")
        
        blob = self.bucket.blob(file_path)
        expiration = datetime.now() + timedelta(minutes=expiration_minutes)
        
        return blob.generate_signed_url(expiration=expiration, method="GET")

    def cleanup_temp_files(self, user_id: str, session_id: str = None):
        """Clean up temporary files"""
        if session_id:
            prefix = f"temp/{user_id}_{session_id}/"
        else:
            prefix = f"temp/{user_id}_"
        
        blobs = self.bucket.list_blobs(prefix=prefix)
        deleted_count = 0
        
        for blob in blobs:
            try:
                blob.delete()
                deleted_count += 1
            except Exception as e:
                logger.warning(f"Failed to delete temp file {blob.name}: {e}")
        
        logger.info(f"Cleaned up {deleted_count} temp files for user {user_id}")

    def _upload_bytes(self, file_path: str, data: bytes, content_type: str = None, encoding: str = None):
        """Upload bytes data to GCS"""
        blob = self.bucket.blob(file_path)

        # Set content type and encoding before upload
        if content_type:
            blob.content_type = content_type
        if encoding:
            blob.content_encoding = encoding

        # Use upload_from_string with explicit content_type parameter
        try:
            blob.upload_from_string(data, content_type=content_type)
            logger.debug(f"Successfully uploaded {file_path} with content_type={content_type}")
        except Exception as e:
            logger.error(f"Failed to upload {file_path}: {e}")
            raise

    def _upload_json(self, file_path: str, data: Dict[str, Any]):
        """Upload JSON data to GCS"""
        json_string = json.dumps(data, indent=2)
        logger.debug(f"Uploading JSON to {file_path}, size: {len(json_string)} chars")

        # Use BytesIO to ensure proper Content-Type handling
        from io import BytesIO
        json_bytes = json_string.encode('utf-8')
        json_file = BytesIO(json_bytes)

        blob = self.bucket.blob(file_path)
        blob.upload_from_file(json_file, content_type="application/json")
        logger.debug(f"Successfully uploaded JSON to {file_path}")

    def _download_json(self, file_path: str) -> Dict[str, Any]:
        """Download and parse JSON from GCS"""
        blob = self.bucket.blob(file_path)
        content = blob.download_as_text()
        return json.loads(content)

    def _download_bytes(self, file_path: str) -> bytes:
        """Download bytes from GCS"""
        blob = self.bucket.blob(file_path)
        return blob.download_as_bytes()
