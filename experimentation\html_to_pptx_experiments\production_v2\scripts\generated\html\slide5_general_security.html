<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Azure Security and Compliance: Protecting Your Assets</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/gojs/release/go.js"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Segoe+UI:wght@400;700&display=swap');
    html, body { height: 100%; margin: 0; }
    body {
      background-color: #f0f2f5;
      font-family: 'Segoe UI','Roboto','Helvetica Neue',Arial,sans-serif;
    }
    .slide-content { /* required main content area */ }
    .diagram-canvas { width: 100%; height: 320px; }
  </style>
</head>
<body class="flex items-center justify-center">
  <div class="w-[1280px] h-[720px] bg-white shadow-2xl overflow-hidden flex flex-col">
    <!-- Header (consistent) -->
    <div class="flex items-center px-10 bg-[#0078D4] text-white" style="height:72px;">
      <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/7157f182669cddd42db4831e747704860e000a9e/svgs/colored-svg-logos/azure-icon.svg" alt="Azure Icon" class="w-8 h-8 mr-4" />
      <div class="font-bold text-[1.2em]">Microsoft Azure</div>
    </div>

    <!-- Content -->
    <div class="slide-content flex-1 px-10 py-5 box-border overflow-hidden">
      <!-- Title aligned top-left -->
      <h1 class="text-[2.4em] leading-tight font-bold text-[#005A9E] mb-3">Azure Security and Compliance: Protecting Your Assets</h1>

      <!-- Two-column layout -->
      <div class="grid grid-cols-2 gap-8 h-[calc(720px-72px-72px)]">
        <!-- Left column: Key points -->
        <div class="flex flex-col space-y-4 overflow-hidden">
          <!-- Azure Security Center / Defender for Cloud -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center mb-2">
              <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/security/Azure-security-03336-icon-service-Microsoft-Defender-EASM.svg" alt="Defender Icon" class="w-5 h-5 mr-2">
              <span class="text-[#0078D4] font-bold text-[1.3em]">Microsoft Defender for Cloud</span>
            </div>
            <p class="text-[1.2em] text-gray-700">
              Unified security management and threat protection. Continuously assess your security posture, prioritize
              recommendations, and harden resources with guided remediation across compute, data, and services.
            </p>
          </div>

          <!-- Azure Active Directory -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center mb-2">
              <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/identity/Azure-identity-10224-icon-service-Active-Directory-Connect-Health.svg" alt="Azure AD Icon" class="w-5 h-5 mr-2">
              <span class="text-[#0078D4] font-bold text-[1.3em]">Azure Active Directory (Entra ID)</span>
            </div>
            <p class="text-[1.2em] text-gray-700">
              Centralized identity and access management. Enforce MFA, Conditional Access, and Single Sign‑On to secure
              access to Azure and SaaS apps with least‑privilege and risk‑based controls.
            </p>
          </div>

          <!-- Azure Firewall -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center mb-2">
              <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/networking/Azure-networking-10084-icon-service-Firewalls.svg" alt="Firewall Icon" class="w-5 h-5 mr-2">
              <span class="text-[#0078D4] font-bold text-[1.3em]">Azure Firewall</span>
            </div>
            <p class="text-[1.2em] text-gray-700">
              Cloud‑native, stateful firewall with threat intelligence. Filter traffic by IP, port, protocol, and
              FQDN/application rules; integrate with Firewall Manager for at‑scale policy governance.
            </p>
          </div>

          <!-- Data Encryption + Compliance -->
          <div class="grid grid-cols-2 gap-4">
            <div class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center mb-2">
                <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/security/Azure-security-10245-icon-service-Key-Vaults.svg" alt="Key Vault Icon" class="w-5 h-5 mr-2">
                <span class="text-[#0078D4] font-bold text-[1.2em]">Data Encryption</span>
              </div>
              <p class="text-[1.2em] text-gray-700">
                Encrypt data at rest and in transit. Manage keys and certificates in Azure Key Vault; use platform
                encryption or Customer‑Managed Keys for granular control.
              </p>
            </div>
            <div class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center mb-2">
                <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/security/Azure-security-10572-icon-service-ExtendedSecurityUpdates.svg" alt="Compliance Icon" class="w-5 h-5 mr-2">
                <span class="text-[#0078D4] font-bold text-[1.2em]">Compliance</span>
              </div>
              <p class="text-[1.2em] text-gray-700">
                Broad certifications (e.g., HIPAA, GDPR, ISO 27001, PCI DSS) support regulated workloads.
                Learn more: <a class="text-[#005A9E] underline" href="https://learn.microsoft.com/azure/compliance/" target="_blank" rel="noopener">Azure Compliance</a>
              </p>
            </div>
          </div>
        </div>

        <!-- Right column: Visuals -->
        <div class="flex flex-col space-y-5 overflow-hidden">
          <!-- Defense-in-Depth Diagram (GoJS) -->
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <div class="flex items-center justify-between mb-2">
              <div class="font-semibold text-[#005A9E] text-[1.1em]">Defense-in-Depth: Layered Security Controls</div>
              <div class="text-xs text-gray-500">Arrows avoid overlap</div>
            </div>
            <div id="securityDiagram" class="diagram-canvas border border-gray-100 rounded"></div>
          </div>

          <!-- Compliance badges -->
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div class="font-semibold text-[#005A9E] text-[1.1em] mb-3">Key Compliance Programs</div>
            <div class="grid grid-cols-4 gap-3">
              <div class="flex items-center justify-center border border-gray-200 rounded p-2 bg-white">
                <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/security/Azure-security-10572-icon-service-ExtendedSecurityUpdates.svg" alt="Compliance" class="w-5 h-5 mr-2">
                <span class="text-[1.1em] text-gray-800 font-semibold">HIPAA</span>
              </div>
              <div class="flex items-center justify-center border border-gray-200 rounded p-2 bg-white">
                <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/security/Azure-security-10572-icon-service-ExtendedSecurityUpdates.svg" alt="Compliance" class="w-5 h-5 mr-2">
                <span class="text-[1.1em] text-gray-800 font-semibold">GDPR</span>
              </div>
              <div class="flex items-center justify-center border border-gray-200 rounded p-2 bg-white">
                <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/security/Azure-security-10572-icon-service-ExtendedSecurityUpdates.svg" alt="Compliance" class="w-5 h-5 mr-2">
                <span class="text-[1.1em] text-gray-800 font-semibold">ISO 27001</span>
              </div>
              <div class="flex items-center justify-center border border-gray-200 rounded p-2 bg-white">
                <img src="https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/security/Azure-security-10572-icon-service-ExtendedSecurityUpdates.svg" alt="Compliance" class="w-5 h-5 mr-2">
                <span class="text-[1.1em] text-gray-800 font-semibold">PCI DSS</span>
              </div>
            </div>
            <div class="mt-3 text-xs text-gray-600">
              For a full list of certifications and regional offerings, visit
              <a class="text-[#005A9E] underline" href="https://learn.microsoft.com/azure/compliance/offerings/" target="_blank" rel="noopener">Azure compliance offerings</a>.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    function initDiagram() {
      const $go = go.GraphObject.make;

      const diagram = $go(go.Diagram, "securityDiagram", {
        initialContentAlignment: go.Spot.Center,
        isReadOnly: true,
        "undoManager.isEnabled": false,
        "animationManager.isEnabled": false,
        padding: 6,
        layout: $go(go.LayeredDigraphLayout, {
          direction: 0,           // Left-to-Right
          layerSpacing: 40,
          columnSpacing: 30,
          setsPortSpots: false
        })
      });

      // Node template
      diagram.nodeTemplate =
        $go(go.Node, "Auto",
          $go(go.Shape, "RoundedRectangle",
            { fill: "white", stroke: "#CBD5E1", strokeWidth: 1, parameter1: 8 }),
          $go(go.Panel, "Horizontal", { margin: 6 },
            $go(go.Picture, { width: 18, height: 18, margin: new go.Margin(0,6,0,0) },
              new go.Binding("source", "icon")),
            $go(go.Panel, "Table",
              $go(go.TextBlock,
                { row: 0, font: "bold 12px Segoe UI, sans-serif", stroke: "#1f2937" },
                new go.Binding("text", "title")),
              $go(go.TextBlock,
                { row: 1, margin: new go.Margin(2,0,0,0), font: "11px Segoe UI, sans-serif", stroke: "#6b7280", wrap: go.TextBlock.WrapFit, width: 170 },
                new go.Binding("text", "desc"))
            )
          )
        );

      // Link template: avoid overlapping arrows
      diagram.linkTemplate =
        $go(go.Link,
          {
            routing: go.Link.AvoidsNodes,
            curve: go.Link.JumpGap,
            corner: 6,
            selectable: false,
            toShortLength: 3
          },
          $go(go.Shape, { stroke: "#64748B", strokeWidth: 1.6 }),
          $go(go.Shape, { toArrow: "Standard", fill: "#64748B", stroke: null, scale: 1 })
        );

      // Icons from tool
      const firewallIcon = "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/networking/Azure-networking-10084-icon-service-Firewalls.svg";
      const adIcon = "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/identity/Azure-identity-10224-icon-service-Active-Directory-Connect-Health.svg";
      const keyVaultIcon = "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/security/Azure-security-10245-icon-service-Key-Vaults.svg";
      const defenderIcon = "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/security/Azure-security-03336-icon-service-Microsoft-Defender-EASM.svg";
      const complianceIcon = "https://raw.githubusercontent.com/DuyquanDuc/Basic-HTML-and-CSS-web-page/88482caf10377ed48e410457e8cc27469a05c410/azure_svg/Icons/security/Azure-security-10572-icon-service-ExtendedSecurityUpdates.svg";

      diagram.model = new go.GraphLinksModel(
        [
          { key: "phys", title: "Physical Security", desc: "Datacenter access controls and hardware protections", icon: "" },
          { key: "net", title: "Network Security", desc: "NSGs, Azure Firewall, private endpoints", icon: firewallIcon },
          { key: "id", title: "Identity & Access", desc: "Azure AD: MFA • SSO • Conditional Access", icon: adIcon },
          { key: "enc", title: "Data Encryption", desc: "At rest & in transit, CMK with Key Vault", icon: keyVaultIcon },
          { key: "threat", title: "Threat Protection", desc: "Defender for Cloud: posture, alerts, guidance", icon: defenderIcon },
          { key: "comp", title: "Compliance & Monitoring", desc: "Policy, audits, evidence and reporting", icon: complianceIcon }
        ],
        [
          { from: "phys", to: "net" },
          { from: "net", to: "id" },
          { from: "id", to: "enc" },
          { from: "enc", to: "threat" },
          { from: "threat", to: "comp" }
        ]
      );
    }

    window.addEventListener('load', initDiagram);
  </script>
</body>
</html>