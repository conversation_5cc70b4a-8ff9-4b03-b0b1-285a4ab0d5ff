# app.py (Updated for WebSocket and Concurrent Slide Processing)

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Depends
from pydantic import BaseModel
from loguru import logger
from fastapi.middleware.cors import CORSMiddleware
from auth.firebase_auth import get_current_user, get_optional_user, FirebaseUser
from storage.cloud_storage_service import CloudStorageService
from pptx_generation.planner import Planner
from pptx_generation.generation import Generator
from htmlrender.renderer import HTMLRenderer
from llm.llmwrapper import LLM
from pptx_generation.html_to_pptx_translator import generate_powerpoint_from_html
# Removed unused imports - now using SlideFixer class
from pptx_generation.slide_fixer import SlideFixer
from prompt.experiment_prompts import slide_fix_orchestrator_prompt
from llm.providers.tavily_image_search import log_tavily_usage_in_html, get_tavily_registry_stats
from dotenv import load_dotenv
import traceback
import os
import sys
import tempfile
from datetime import datetime
from fastapi.responses import FileResponse
import asyncio # Import asyncio
from typing import List, Dict, Any, Union, Optional # Added for type hinting clarity

load_dotenv()

# Configure loguru for Google Cloud Logging
# Remove default handler and add stdout handler for Cloud Run
logger.remove()
logger.add(
    sys.stdout,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
    level="INFO",
    serialize=False  # Keep as text for Cloud Logging
)

class UserInput(BaseModel):
    user_input: str

class OutlineInput(BaseModel):
    outline: str
    user_input: str = "Create a presentation"  # Optional, with default

class SlidesInput(BaseModel):
    slides: list
    user_input: str = "Create a presentation"  # Optional, with default

class HTMLInput(BaseModel):
    html_slides: list

class FixSlideRequest(BaseModel):
    slide_number: int
    current_html: str
    fix_instruction: str
    original_slide_content: Optional[str] = None  # Original slide content from planner
    user_prompt: Optional[str] = None  # Original user prompt
    style_reference_slides: Optional[List[Dict[str, Any]]] = None  # Other slides for style consistency

class FixSlideResponse(BaseModel):
    success: bool
    fixed_html: str
    tools_used: List[str]
    reasoning: str
    error_message: Optional[str] = None

class PresentationResponse(BaseModel):
    outline: str = None
    slides: list = None
    html_slides: list = None
    presentation_js: str = None
    download_url: str = None
    status: str
    message: str
    # Add fields for token counts at the top level for overall process
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    # Add a field for detailed token counts per step/slide
    token_details: Dict[str, Any] = {}


app = FastAPI(
    title="SlideGen API",
    description="Generate slide outlines and rendered HTML from a user query",
    version="0.1",
    debug=True
)

origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add root endpoint for health check
@app.get("/")
async def root():
    return {"message": "SlideGen API is running", "status": "healthy"}

# Add health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "API is operational"}

# Helper functions for slide fixing
# NOTE: These functions are defined before global LLM instances for historical reasons
# They now accept LLM instances as parameters to avoid dependency issues
# Helper functions for slide fixing - now using SlideFixer class methods
def parse_orchestrator_response(response_text: str) -> Dict[str, Any]:
    """Parse the orchestrator's structured response using SlideFixer"""
    return slide_fixer.parse_orchestrator_response(response_text)

def is_error_slide(html_content: str) -> bool:
    """Check if the HTML content represents an error slide using SlideFixer"""
    return slide_fixer.is_error_slide(html_content)

def extract_style_context(style_reference_slides: List[Dict[str, Any]]) -> str:
    """Extract style information from reference slides using SlideFixer"""
    return slide_fixer.extract_style_context(style_reference_slides)

# Initialize slide fixer instance
slide_fixer = SlideFixer()

async def execute_slide_fix_tools(html_content: str, user_instructions: str, tools_to_execute: List[str], llm_instance: LLM, renderer_instance: HTMLRenderer, slide_number: int = None, original_slide_content: str = None, user_prompt: str = None, style_reference_slides: List[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Execute the selected tools in order using the new SlideFixer class"""
    return await slide_fixer.execute_slide_fix_tools(
        html_content=html_content,
        user_instructions=user_instructions,
        tools_to_execute=tools_to_execute,
        llm_instance=llm_instance,
        slide_number=slide_number,
        original_slide_content=original_slide_content,
        user_prompt=user_prompt,
        style_reference_slides=style_reference_slides
    )

# Initialize your LLMs, planner, generator, renderer once
#llm_outline = LLM(provider="anthropic", model="claude-opus-4-20250514",temperature=0.1, max_tokens=2000)
#llm_generate = LLM(provider="deepseek", model="deepseek/deepseek-r1-0528:free")

llm_outline = LLM(provider="vertex_gemini", model="gemini-2.5-flash")
llm_generate = LLM(provider="vertex_gemini", model="gemini-2.5-pro")
llm_translate = LLM(provider="vertex_gemini", model="gemini-2.5-flash")
llm_fix = LLM(provider="vertex_gemini", model="gemini-2.5-flash")  # Dedicated LLM for slide fixing
planner = Planner()
generator = Generator()
renderer = HTMLRenderer() # Initialize here, ensure it can be reused across async calls

# Initialize Cloud Storage Service
storage_service = CloudStorageService()

# Global dictionary to accumulate token counts per request (for simplicity; in production, use a more robust tracking)
# This will be reset per main request (e.g., /generate-slides or /ws/generate-presentation)
# For a more robust solution, you'd pass a token_tracker object through functions.
global_token_tracker = {
    "outline_input": 0, "outline_output": 0,
    "brainstorm_input": 0, "brainstorm_output": 0,
    "slide_content_input": 0, "slide_content_output": 0,
    "title_slide_gen_input": 0, "title_slide_gen_output": 0,
    "agenda_slide_gen_input": 0, "agenda_slide_gen_output": 0,
    "general_slide_gen_input": 0, "general_slide_gen_output": 0,
    "reviewer_input": 0, "reviewer_output": 0,
    "translation_input": 0, "translation_output": 0,
    "slide_renders": {} # To store token counts per individual slide generation
}

# Reset the tracker for each new request
def reset_token_tracker():
    global global_token_tracker
    global_token_tracker = {
        "outline_input": 0, "outline_output": 0,
        "brainstorm_input": 0, "brainstorm_output": 0,
        "slide_content_input": 0, "slide_content_output": 0,
        "title_slide_gen_input": 0, "title_slide_gen_output": 0,
        "agenda_slide_gen_input": 0, "agenda_slide_gen_output": 0,
        "general_slide_gen_input": 0, "general_slide_gen_output": 0,
        "reviewer_input": 0, "reviewer_output": 0,
        "translation_input": 0, "translation_output": 0,
        "slide_renders": {}
    }

# --- Helper functions for concurrent processing ---

# This function calls the generator to get HTML content for a slide.
# It's called by _process_and_render_single_slide.
async def _generate_slide_html_async(
    slide_number: int,
    slide_content: str,
    user_query: str,
    # existing_slide_html_context: List[Dict[str, Any]], # <-- REMOVED THIS LINE
    generator_instance: Generator,
    generator_llm: LLM,
    brainstorm_output: str
) -> Dict[str, Any]:
    """Generates HTML content for a single slide and returns a dict with HTML and token counts."""
    common_params = {
        'query': user_query,
        'slide_content': slide_content,
        'generator_llm': generator_llm,
        'reviewer_llm': generator_llm,
        'review': False, # Set to True if you want a review step
        'brainstorm_output': brainstorm_output
    }

    generated_data = {}

    if slide_number == 1:
        print(f"🔄 DEBUG: Calling generate_title_slide with LLM provider: {generator_llm.provider}")
        generated_data = await generator_instance.generate_title_slide(**common_params)
    elif slide_number == 2:
        # title_slide_html = "" # <-- REMOVED THIS LINE
        # for s in existing_slide_html_context: # <-- REMOVED THIS LOOP
        #     if s.get('slide_number') == 1:
        #         title_slide_html = s.get('html_content', '')
        #         break
        generated_data = await generator_instance.generate_agenda_slide(
            # title_slide_html=title_slide_html, # <-- REMOVED THIS ARGUMENT
            **common_params
        )
    else:
        # mapped_context = [] # <-- REMOVED THIS LINE
        # for slide in existing_slide_html_context: # <-- REMOVED THIS LOOP
        #     mapped_context.append({
        #         'name': slide.get('name', f"Slide {slide.get('slide_number', 'Unknown')}"),
        #         'html': slide.get('html_content', '')
        #     })

        generated_data = await generator_instance.generate_general_slide(
            # existing_slide_content=mapped_context, # This was already commented out, keeping it that way.
            **common_params
        )

    html_code = generated_data.get('html_code', '')
    input_tokens = generated_data.get('input_token_count', 0)
    output_tokens = generated_data.get('output_token_count', 0)

    return {
        "html_code": html_code,
        "input_tokens": input_tokens,
        "output_tokens": output_tokens
    }

async def _process_and_render_single_slide(
    slide_data: Dict[str, Any],
    user_query: str,
    llm_generate: LLM,
    generator_instance: Generator,
    renderer_instance: HTMLRenderer,
    # existing_slide_html_context: List[Dict[str, Any]], # <-- This line is already commented out, now it's formally removed
    brainstorm_output: str
) -> Dict[str, Any]:
    """Generates HTML for a single slide and formats its result, including token counts."""
    slide_number = slide_data.get('slide_number')
    slide_content_text = slide_data.get('content')
    original_slide_title = slide_data.get('title', f'Slide {slide_number}')

    try:
        print(f"🔄 DEBUG: Starting HTML rendering for slide {slide_number} - '{original_slide_title}'")
        print(f"🔧 DEBUG: LLM provider in _process_and_render_single_slide: {llm_generate.provider}")
        print(f"🔧 DEBUG: LLM instance type: {type(llm_generate.llm)}")

        generation_result = await _generate_slide_html_async(
            slide_number,
            slide_content_text,
            user_query,
            # existing_slide_html_context, # <-- REMOVED THIS ARGUMENT from the call
            generator_instance,
            llm_generate,
            brainstorm_output
        )

        generated_html = generation_result['html_code']
        slide_render_input_tokens = generation_result['input_tokens']
        slide_render_output_tokens = generation_result['output_tokens']

        result_data = {
            "slide_number": slide_number,
            "title": original_slide_title,
            "name": original_slide_title,
            "content": slide_content_text,
            "html_content": generated_html,
            "original_slide": slide_data,
            "status": "completed",
            "message": f"Successfully rendered slide {slide_number}",
            "token_counts": {
                "input_tokens": slide_render_input_tokens,
                "output_tokens": slide_render_output_tokens,
            }
        }
        return result_data
    except Exception as e:
        print(f"Error in _process_and_render_single_slide for slide {slide_number}: {e}")
        traceback.print_exc()
        return {
            "slide_number": slide_number,
            "title": original_slide_title,
            "name": original_slide_title,
            "content": slide_content_text,
            "html_content": f"<div style='padding: 20px; color: red;'>Error rendering slide {slide_number}: {str(e)}</div>",
            "original_slide": slide_data,
            "status": "failed",
            "error": str(e),
            "message": f"Failed to render slide {slide_number}",
            "token_counts": {"input_tokens": 0, "output_tokens": 0}
        }
# --- Existing Endpoints (mostly unchanged, except for generate-slides) ---

@app.post("/generate-outline", response_model=PresentationResponse) # Specify response_model
async def generate_outline_endpoint(req: UserInput):
    reset_token_tracker() # Reset for a new request
    try:
        print(f"✅ API call received for outline generation. Input: '{req.user_input}'")

        # DEBUG: Log LLM provider details
        print(f"🔧 DEBUG: LLM provider: {llm_outline.provider}")
        print(f"🔧 DEBUG: LLM instance type: {type(llm_outline.llm)}")
        if hasattr(llm_outline.llm, 'provider_type'):
            print(f"🔧 DEBUG: Provider type: {llm_outline.llm.provider_type}")
        if hasattr(llm_outline.llm, 'project_id'):
            print(f"🔧 DEBUG: Project ID: {llm_outline.llm.project_id}")

        # Check environment variables
        import os
        gemini_key = os.getenv('GEMINI_API_KEY')
        gcp_project = os.getenv('GOOGLE_CLOUD_PROJECT')
        print(f"🔧 DEBUG: GEMINI_API_KEY present: {bool(gemini_key)}")
        print(f"🔧 DEBUG: GOOGLE_CLOUD_PROJECT: {gcp_project}")

        pptx_plan = await planner.outline(query=req.user_input, llm=llm_outline)

        if "response" in pptx_plan and isinstance(pptx_plan["response"], str):
            # Capture token counts from planner.outline directly from pptx_plan
            outline_input_token_count = pptx_plan.get('input_token_count', 0)
            outline_output_token_count = pptx_plan.get('output_token_count', 0)
            
            # Update global tracker for overall context (though for this endpoint, it's just these tokens)
            global_token_tracker["outline_input"] += outline_input_token_count
            global_token_tracker["outline_output"] += outline_output_token_count

            return PresentationResponse(
                outline=pptx_plan["response"], # This is correct for the outline string
                status="success",
                message="Outline generated successfully.",
                # Use the directly captured token counts for this response
                total_input_tokens=outline_input_token_count, # Corrected to use the specific outline tokens
                total_output_tokens=outline_output_token_count, # Corrected to use the specific outline tokens
                token_details={
                    "outline_generation": {
                        "input_tokens": outline_input_token_count,
                        "output_tokens": outline_output_token_count
                    }
                }
            )
        else:
            print(f"❌ Unexpected response format from planner: {pptx_plan}")
            raise HTTPException(status_code=500, detail="Internal server error: Invalid format from planner.")

    except Exception as e:
        print(f"❌ An error occurred: {e}")
        print(f"❌ Error type: {type(e).__name__}")

        # Check if this is the Gemini API error
        error_str = str(e).lower()
        if "generativelanguage.googleapis.com" in error_str:
            print("🚨 CRITICAL: Error from direct Gemini API detected!")
            print("🚨 This means Vertex AI is not being used properly")
        elif "api key" in error_str and "expired" in error_str:
            print("🚨 CRITICAL: API key error detected!")
            print("🚨 This should not happen with Vertex AI")

        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate-slides", response_model=PresentationResponse) # Specify response_model
async def generate_slides_endpoint(req: OutlineInput):
    reset_token_tracker() # Reset for a new request
    user_query = req.user_input
    MAX_RETRIES = 2
    try:
        print(f"✅ API call received for slide content generation. Outline length: {len(req.outline)} chars")

        # The brainstorm_response here is a hardcoded string, not an LLM call, so no tokens for it.
        # brainstorm_response = "Technical approach to create a comprehensive presentation."

        slide_content_output = None
        processed_slide_content = None
        slide_content_input_token_count = 0
        slide_content_output_token_count = 0

        attempts = 0
        success = False

        while attempts < MAX_RETRIES and not success:
            attempts += 1
            print(f"Attempt {attempts}/{MAX_RETRIES} to generate and process slide content...")

            try:
                slide_content_output = await planner.slide_content(
                    query=user_query,
                    #brainstorm_response=brainstorm_response,
                    outline_response=req.outline,
                    llm=llm_generate
                )

                # Capture token counts for this specific slide content planning attempt
                current_input_tokens = slide_content_output.get('input_token_count', 0)
                current_output_tokens = slide_content_output.get('output_token_count', 0)

                # Accumulate tokens in the global tracker across all attempts
                global_token_tracker["slide_content_input"] += current_input_tokens
                global_token_tracker["slide_content_output"] += current_output_tokens
                slide_content_input_token_count += current_input_tokens
                slide_content_output_token_count += current_output_tokens

                processed_slide_content = planner.extract_slide_content(
                    slide_content_response=slide_content_output['response']
                )

                success = True

            except Exception as e:
                print(f"⚠️ Attempt {attempts} failed during slide content generation/processing: {e}")
                traceback.print_exc()
            
                if attempts < MAX_RETRIES:
                    print("Retrying slide content generation...")
                    # Optionally add a small delay before retrying
                    # await asyncio.sleep(1) # Uncomment if you want a pause
                else:
                    # All attempts failed, re-raise the original exception
                    print(f"❌ All {MAX_RETRIES} attempts failed for slide content generation. Aborting.")
                    raise # Re-raise the last exception

        if not success:
            raise Exception("Failed to generate and process slide content after multiple attempts.")

        slides = []
        slide_content_dict = processed_slide_content['slide_content']

        for slide_key, slide_content in slide_content_dict.items():
            slide_number = int(slide_key.split('_')[1])

            slide_data = {
                "slide_number": slide_number,
                "title": slide_content.split('\n')[0].replace('**', '').strip() if slide_content else f"Slide {slide_number}", # Extract title cleanly
                "content": slide_content,
                "slide_type": "title" if slide_number == 1 else "content"
            }
            slides.append(slide_data)

        slides.sort(key=lambda x: x['slide_number'])

        print(f"✅ Generated {len(slides)} textual slides from planner")

        return PresentationResponse(
            outline=req.outline,
            slides=slides,
            status="success",
            message=f"Generated {len(slides)} textual slides from planner. Proceed to WebSocket for rendering.",
            total_input_tokens=global_token_tracker["slide_content_input"], # Only slide content planning tokens here
            total_output_tokens=global_token_tracker["slide_content_output"],
            token_details={
                "slide_content_planning": {
                    "input_tokens": slide_content_input_token_count,
                    "output_tokens": slide_content_output_token_count
                }
            }
        )

    except Exception as e:
        print(f"❌ An error occurred in slide content generation: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# --- NEW WEBSOCKET ENDPOINT ---
@app.websocket("/ws/generate-presentation")
async def websocket_generate_presentation(websocket: WebSocket):
    await websocket.accept()
    print("✅ WebSocket connection accepted.")
    print(f"🔧 DEBUG: WebSocket LLM providers - outline: {llm_outline.provider}, generate: {llm_generate.provider}")
    reset_token_tracker() # Reset for a new WebSocket connection

    # Initialize variables for GCS upload
    current_user = None
    presentation_id = None
    generated_slides = []

    try:
        # First, receive the initial request from the client
        initial_data = await websocket.receive_json()
        user_input = initial_data.get("user_input", "Create a presentation")
        slides_to_render = initial_data.get("slides")

        # Try to get user authentication (optional for WebSocket)
        auth_token = initial_data.get("auth_token")
        print(f"🔍 WebSocket Debug - Received initial_data keys: {list(initial_data.keys())}")
        print(f"🔍 WebSocket Debug - auth_token present: {auth_token is not None}")
        if auth_token:
            print(f"🔍 WebSocket Debug - auth_token length: {len(auth_token)}")
            try:
                from firebase_admin import auth
                decoded_token = auth.verify_id_token(auth_token)
                current_user = FirebaseUser(
                    uid=decoded_token['uid'],
                    email=decoded_token.get('email', ''),
                    name=decoded_token.get('name', '')
                )
                print(f"✅ WebSocket authenticated user: {current_user.uid}")
            except Exception as e:
                print(f"⚠️ WebSocket authentication failed: {e}")
                current_user = None
        else:
            print("⚠️ WebSocket running without authentication - no auth_token in initial_data")

        if not slides_to_render:
            await websocket.send_json({"status": "error", "message": "No slides provided for rendering."})
            await websocket.close()
            return

        print(f"✅ Received {len(slides_to_render)} slides for WebSocket rendering.")

        # --- STEP 0: Perform Brainstorming (before any slide generation) ---
        await websocket.send_json({"type": "status", "message": "Starting brainstorming..."})
        print(f"🔄 DEBUG: About to call planner.brainstorm with LLM provider: {llm_outline.provider}")
        brainstorm_result = await planner.brainstorm(query=user_input, llm=llm_outline)
        print(f"✅ DEBUG: planner.brainstorm completed successfully")
        brainstorm_output = brainstorm_result['response']

        # Accumulate brainstorm token counts
        global_token_tracker["brainstorm_input"] += brainstorm_result.get('input_token_count', 0)
        global_token_tracker["brainstorm_output"] += brainstorm_result.get('output_token_count', 0)
        await websocket.send_json({
            "type": "status",
            "message": "Brainstorming complete.",
            "token_details": {
                "brainstorm_generation": {
                    "input_tokens": brainstorm_result.get('input_token_count', 0),
                    "output_tokens": brainstorm_result.get('output_token_count', 0)
                }
            }
        })
        print(f"Brainstorming completed: {brainstorm_output[:100]}...") # Log a snippet

        # This list will accumulate the rendered HTML content for context
        # rendered_html_context: List[Dict[str, Any]] = [] # <--- REMOVE THIS LINE

        # Stage 1: Process Title Slide (Slide 1) - Await its completion
        title_slide_data = next((s for s in slides_to_render if s.get('slide_number') == 1), None)
        if title_slide_data:
            print(f"🔄 Starting Title Slide (1) processing.")
            print(f"🔧 Using LLM provider: {llm_generate.provider}")
            print(f"🔧 LLM instance type: {type(llm_generate.llm)}")
            title_slide_result = await _process_and_render_single_slide(
                title_slide_data,
                user_input,
                llm_generate,
                generator,
                renderer,
                # [], # <--- REMOVED THIS ARGUMENT
                brainstorm_output
            )
            # Send the result to the client
            await websocket.send_json({"type": "slide_update", "data": title_slide_result})
            # Add to context if successfully completed
            if title_slide_result.get("status") == "completed":
                # Collect slide for GCS upload
                generated_slides.append({
                    'slide_number': title_slide_result['slide_number'],
                    'slide_title': title_slide_result.get('name', f"Slide {title_slide_result['slide_number']}"),
                    'html_content': title_slide_result['html_content']
                })

                # Accumulate token counts
                global_token_tracker["title_slide_gen_input"] += title_slide_result['token_counts']['input_tokens']
                global_token_tracker["title_slide_gen_output"] += title_slide_result['token_counts']['output_tokens']
                global_token_tracker["slide_renders"][f"slide_{title_slide_result['slide_number']}"] = title_slide_result['token_counts']

            print(f"Completed Title Slide (1). Status: {title_slide_result.get('status')}")
        else:
            await websocket.send_json({"type": "status", "message": "Warning: Title slide (1) not found in initial data."})

        # Stage 2: Process Agenda Slide (Slide 2) - Await its completion
        agenda_slide_data = next((s for s in slides_to_render if s.get('slide_number') == 2), None)
        if agenda_slide_data:
            print(f"Starting Agenda Slide (2) processing.")
            agenda_slide_result = await _process_and_render_single_slide(
                agenda_slide_data,
                user_input,
                llm_generate,
                generator,
                renderer,
                # rendered_html_context, # <--- REMOVED THIS ARGUMENT
                brainstorm_output
            )
            # Send the result to the client
            await websocket.send_json({"type": "slide_update", "data": agenda_slide_result})
            # Add to context if successfully completed
            if agenda_slide_result.get("status") == "completed":
                # Collect slide for GCS upload
                generated_slides.append({
                    'slide_number': agenda_slide_result['slide_number'],
                    'slide_title': agenda_slide_result.get('name', f"Slide {agenda_slide_result['slide_number']}"),
                    'html_content': agenda_slide_result['html_content']
                })

                # Accumulate token counts
                global_token_tracker["agenda_slide_gen_input"] += agenda_slide_result['token_counts']['input_tokens']
                global_token_tracker["agenda_slide_gen_output"] += agenda_slide_result['token_counts']['output_tokens']
                global_token_tracker["slide_renders"][f"slide_{agenda_slide_result['slide_number']}"] = agenda_slide_result['token_counts']

            print(f"Completed Agenda Slide (2). Status: {agenda_slide_result.get('status')}")
        else:
            await websocket.send_json({"type": "status", "message": "Warning: Agenda slide (2) not found in initial data."})

        # Stage 3: Process remaining General Slides concurrently
        general_slides_data = [s for s in slides_to_render if s.get('slide_number') not in [1, 2]]
        general_slides_data.sort(key=lambda x: x['slide_number']) # Sorting still good for consistent logging/processing order

        concurrent_tasks = []
        for slide_data in general_slides_data:
            task = asyncio.create_task(_process_and_render_single_slide(
                slide_data,
                user_input,
                llm_generate,
                generator,
                renderer,
                # rendered_html_context[:] # <--- REMOVED THIS ARGUMENT
                brainstorm_output
            ))
            concurrent_tasks.append(task)

        print(f"Launched {len(concurrent_tasks)} general slide rendering tasks concurrently.")

        # Use asyncio.as_completed to get results as they finish
        for future in asyncio.as_completed(concurrent_tasks):
            result = await future
            # Send the result to the client immediately
            await websocket.send_json({"type": "slide_update", "data": result})

            if result.get("status") == "completed":
                # Collect slide for GCS upload
                generated_slides.append({
                    'slide_number': result['slide_number'],
                    'slide_title': result.get('name', f"Slide {result['slide_number']}"),
                    'html_content': result['html_content']
                })

                # No longer appending/sorting rendered_html_context
                global_token_tracker["general_slide_gen_input"] += result['token_counts']['input_tokens']
                global_token_tracker["general_slide_gen_output"] += result['token_counts']['output_tokens']
                global_token_tracker["slide_renders"][f"slide_{result['slide_number']}"] = result['token_counts']

            print(f"Sent update for slide {result.get('slide_number', 'unknown')}. Status: {result.get('status')}")

        # Send final status and total token counts
        # Token calculations remain correct as they sum from global_token_tracker
        total_input = sum(v for k, v in global_token_tracker.items() if 'input' in k and 'slide_renders' not in k) + sum(d['input_tokens'] for d in global_token_tracker['slide_renders'].values())
        total_output = sum(v for k, v in global_token_tracker.items() if 'output' in k and 'slide_renders' not in k) + sum(d['output_tokens'] for d in global_token_tracker['slide_renders'].values())
        logger.info(f"Generation complete. Total Input Tokens for this request: {total_input}, Total Output Tokens for this request: {total_output}")

        # Auto-upload generated HTML slides to GCS if user is authenticated
        if current_user and generated_slides:
            try:
                # Sort slides by slide number
                generated_slides.sort(key=lambda x: x['slide_number'])

                # Create presentation data
                presentation_data = {
                    "title": generated_slides[0].get('slide_title', 'Untitled Presentation') if generated_slides else 'Untitled Presentation',
                    "slides": generated_slides,
                    "user_input": user_input,
                    "outline": ""  # WebSocket doesn't have outline, could be added later
                }

                # Save to GCS
                presentation_id = storage_service.save_presentation(current_user.uid, presentation_data)
                logger.info(f"✅ Auto-uploaded {len(generated_slides)} slides to GCS for user {current_user.uid}, presentation_id: {presentation_id}")

                # Notify client about successful upload
                await websocket.send_json({
                    "type": "gcs_upload",
                    "message": f"Presentation saved to cloud storage with {len(generated_slides)} slides",
                    "presentation_id": presentation_id
                })

            except Exception as gcs_error:
                logger.error(f"❌ Failed to upload slides to GCS: {gcs_error}")
                await websocket.send_json({
                    "type": "gcs_upload_error",
                    "message": f"Failed to save presentation to cloud storage: {str(gcs_error)}"
                })
        elif not current_user:
            logger.info("⚠️ Skipping GCS upload - user not authenticated")
        elif not generated_slides:
            logger.info("⚠️ Skipping GCS upload - no slides generated")

        # Log Tavily usage summary
        tavily_stats = get_tavily_registry_stats()
        logger.info(f"📊 Tavily Usage Summary: {tavily_stats['total_tavily_images']} images from {len(tavily_stats['search_terms'])} searches")
        if tavily_stats['search_terms']:
            logger.info(f"   🔍 Search terms used: {', '.join(tavily_stats['search_terms'])}")
        await websocket.send_json({
            "type": "final_status",
            "message": "All slides processed and rendered.",
            "total_input_tokens": total_input,
            "total_output_tokens": total_output,
            "token_details": global_token_tracker # Send all accumulated details
        })
        print("✅ All slides processed and sent via WebSocket.")

    except WebSocketDisconnect:
        print("❌ WebSocket disconnected.")
    except Exception as e:
        print(f"❌ An error occurred in WebSocket processing: {e}")
        traceback.print_exc() # Print full traceback for server-side debugging
        try:
            # Also send error details to the client including current token totals
            total_input = sum(v for k, v in global_token_tracker.items() if 'input' in k and 'slide_renders' not in k) + sum(d['input_tokens'] for d in global_token_tracker['slide_renders'].values())
            total_output = sum(v for k, v in global_token_tracker.items() if 'output' in k and 'slide_renders' not in k) + sum(d['output_tokens'] for d in global_token_tracker['slide_renders'].values())
            logger.info(f"Generation complete. Total Input Tokens for this request: {total_input}, Total Output Tokens for this request: {total_output}")
            await websocket.send_json({
                "type": "error",
                "message": str(e),
                "detail": traceback.format_exc(),
                "total_input_tokens": total_input,
                "total_output_tokens": total_output,
                "token_details": global_token_tracker
            })
        except RuntimeError:
            print("Could not send error message, WebSocket already closed.")
    finally:
        await websocket.close()
        print("✅ WebSocket connection closed.")
# --- Remaining Endpoints (unchanged, as they operate on final data) ---

@app.post("/fix-slide")
async def fix_slide_endpoint(req: FixSlideRequest, current_user: FirebaseUser = Depends(get_optional_user)):
    """Fix a specific slide using LLM orchestrator to decide which tools to use"""
    try:
        print(f"🔧 Fix slide request received for slide {req.slide_number}")
        print(f"📝 User instruction: {req.fix_instruction}")
        print(f"� Fix slide request received for slide {req.slide_number}")
        print(f"� User instruction: {req.fix_instruction}")
        print(f"🎨 Style reference slides: {len(req.style_reference_slides) if req.style_reference_slides else 0} slides")

        # Step 1: Use LLM orchestrator to decide strategy
        # Use global LLM instance for consistency
        orchestrator_renderer = HTMLRenderer(size=(1280, 720))

        # Render current HTML to image for analysis - ensure temp directory exists
        temp_orchestrator_filename = f"temp_orchestrator_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

        # Ensure we can write to the current directory in Cloud Run
        os.makedirs(".", exist_ok=True)

        html_image = orchestrator_renderer.renderHTML(req.current_html, temp_orchestrator_filename)

        # Keep track of temp file for cleanup
        temp_file_to_cleanup = temp_orchestrator_filename

        # Get orchestrator decision
        orchestrator_prompt = slide_fix_orchestrator_prompt.format(
            code=req.current_html,
            user_instructions=req.fix_instruction
        )

        print(f"🧠 Asking orchestrator to analyze slide...")
        orchestrator_response = await llm_fix.call_with_images(
            query=orchestrator_prompt,
            images=[html_image]
        )

        print(f"🧠 Orchestrator response: {orchestrator_response['text'][:200]}...")

        # Step 2: Parse orchestrator decision
        strategy = parse_orchestrator_response(orchestrator_response['text'])
        print(f"📋 Strategy: {strategy}")

        # Step 3: Execute selected tools
        execution_result = await execute_slide_fix_tools(
            req.current_html,
            req.fix_instruction,
            strategy['execution_order'],
            llm_fix,       # Use dedicated LLM instance for fixing
            renderer,      # Use global renderer instance
            req.slide_number,  # Pass slide number for potential regeneration
            req.original_slide_content,  # Pass original slide content for regeneration
            req.user_prompt,  # Pass original user prompt for regeneration
            req.style_reference_slides  # Pass reference slides for style consistency
        )

        if execution_result['success']:
            print(f"✅ Slide fix completed. Tools used: {execution_result['tools_used']}")

            # Auto-upload fixed slide to GCS if user is authenticated
            if current_user:
                try:
                    # Create a single-slide presentation for the fixed slide
                    fixed_slide_data = {
                        "title": f"Fixed Slide {req.slide_number}",
                        "slides": [{
                            'slide_number': req.slide_number,
                            'slide_title': f"Fixed Slide {req.slide_number}",
                            'html_content': execution_result['fixed_html']
                        }],
                        "user_input": req.fix_instruction,
                        "outline": ""
                    }

                    # Save to GCS
                    presentation_id = storage_service.save_presentation(current_user.uid, fixed_slide_data)
                    logger.info(f"✅ Auto-uploaded fixed slide {req.slide_number} to GCS for user {current_user.uid}, presentation_id: {presentation_id}")

                except Exception as gcs_error:
                    logger.error(f"❌ Failed to upload fixed slide to GCS: {gcs_error}")
            else:
                logger.info("⚠️ Skipping GCS upload for fixed slide - user not authenticated")

            return FixSlideResponse(
                success=True,
                fixed_html=execution_result['fixed_html'],
                tools_used=execution_result['tools_used'],
                reasoning=strategy['reasoning']
            )
        else:
            print(f"❌ Slide fix failed: {execution_result.get('error', 'Unknown error')}")
            return FixSlideResponse(
                success=False,
                fixed_html=req.current_html,
                tools_used=execution_result['tools_used'],
                reasoning=strategy['reasoning'],
                error_message=execution_result.get('error', 'Unknown error')
            )

    except Exception as e:
        print(f"❌ Error in fix-slide endpoint: {e}")
        traceback.print_exc()
        return FixSlideResponse(
            success=False,
            fixed_html=req.current_html,
            tools_used=[],
            reasoning="Error occurred during processing",
            error_message=str(e)
        )
    finally:
        # Clean up temp file
        try:
            if 'temp_file_to_cleanup' in locals() and os.path.exists(temp_file_to_cleanup):
                os.remove(temp_file_to_cleanup)
                print(f"✅ Cleaned up temp file: {temp_file_to_cleanup}")
        except Exception as cleanup_error:
            print(f"⚠️ Failed to clean up temp file: {cleanup_error}")

@app.post("/export-powerpoint", response_model=PresentationResponse)
async def export_powerpoint_endpoint(req: HTMLInput, current_user: FirebaseUser = Depends(get_current_user)):
    reset_token_tracker() # Reset for a new request
    try:
        print(f"✅ API call received for PowerPoint export from user {current_user.uid}. {len(req.html_slides)} slides to export")

        # Log Tavily image usage in all slides being exported to PowerPoint
        for slide_data in req.html_slides:
            html_content = slide_data.get('html_content', '')
            slide_number = slide_data.get('slide_number', 'unknown')
            logger.info(f"🔍 Analyzing PowerPoint slide {slide_number} for Tavily image usage...")
            log_tavily_usage_in_html(html_content, slide_number)

        # Save presentation to cloud storage first
        presentation_data = {
            "title": req.html_slides[0].get('slide_title', 'Untitled Presentation') if req.html_slides else 'Untitled Presentation',
            "slides": req.html_slides,
            "user_input": getattr(req, 'user_input', ''),
            "outline": getattr(req, 'outline', '')
        }
        presentation_id = storage_service.save_presentation(current_user.uid, presentation_data)

        # The generate_powerpoint_from_html needs to return token counts
        # This function is in html_to_pptx_translator.py, so it also needs modification.
        # Assuming it returns {'presentation_js': ..., 'download_url': ..., 'input_tokens': N, 'output_tokens': M}
        result = await generate_powerpoint_from_html(req.html_slides, llm_translate)

        # If the result contains actual file data, save it to cloud storage
        if 'file_data' in result:
            download_url = storage_service.save_export_file(
                current_user.uid,
                presentation_id,
                result['file_data'],
                'powerpoint'
            )
        else:
            # Fallback to original download URL if no file data
            download_url = result.get('download_url', '')

        # Capture token counts for translation
        translation_input_token_count = result.get('input_token_count', 0)
        translation_output_token_count = result.get('output_token_count', 0)
        global_token_tracker["translation_input"] += translation_input_token_count
        global_token_tracker["translation_output"] += translation_output_token_count

        return PresentationResponse(
            html_slides=req.html_slides,
            presentation_js=result['presentation_js'],
            download_url=download_url,
            status="success",
            message=f"Generated PowerPoint file for {len(req.html_slides)} slides",
            total_input_tokens=global_token_tracker["translation_input"],
            total_output_tokens=global_token_tracker["translation_output"],
            token_details={
                "powerpoint_translation": {
                    "input_tokens": translation_input_token_count,
                    "output_tokens": translation_output_token_count
                }
            }
        )

    except Exception as e:
        print(f"❌ An error occurred in PowerPoint export: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/export-pdf", response_model=PresentationResponse)
async def export_pdf_endpoint(req: HTMLInput, current_user: FirebaseUser = Depends(get_current_user)):
    reset_token_tracker() # Reset for a new request
    try:
        print(f"✅ API call received for PDF export from user {current_user.uid}. {len(req.html_slides)} slides to export")

        # Save presentation to cloud storage first
        presentation_data = {
            "title": req.html_slides[0].get('slide_title', 'Untitled Presentation') if req.html_slides else 'Untitled Presentation',
            "slides": req.html_slides,
            "user_input": getattr(req, 'user_input', ''),
            "outline": getattr(req, 'outline', '')
        }
        presentation_id = storage_service.save_presentation(current_user.uid, presentation_data)

        from htmlrender.renderer import HTMLRenderer

        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.units import inch
        except ImportError:
            raise HTTPException(
                status_code=500,
                detail="PDF export requires 'reportlab' library. Please install it with: pip install reportlab"
            )

        # Use larger viewport to capture more content and avoid cutoffs
        pdf_renderer = HTMLRenderer()  # Uses default (1920, 1080) size

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_filename = f"presentation_{timestamp}.pdf"

        # Use system temporary directory for GCP Cloud Run compatibility
        temp_dir = tempfile.gettempdir()
        pdf_file_path = os.path.join(temp_dir, pdf_filename)

        # Use 16:9 aspect ratio for presentation dimensions (matches 1280x720)
        # Standard presentation size: 10" x 5.625" (16:9 ratio)
        presentation_width = 10 * inch
        presentation_height = 5.625 * inch
        c = canvas.Canvas(pdf_file_path, pagesize=(presentation_width, presentation_height))
        page_width, page_height = presentation_width, presentation_height

        # Keep track of temp files to clean up later
        temp_files_to_cleanup = []

        for i, slide_data in enumerate(req.html_slides):
            try:
                html_content = slide_data.get('html_content', '')
                slide_number = slide_data.get('slide_number', i+1)

                print(f"🔄 Rendering slide {slide_number} to image for PDF...")

                # Log Tavily image usage in final slide content
                logger.info(f"🔍 Analyzing final slide {slide_number} for Tavily image usage...")
                log_tavily_usage_in_html(html_content, slide_number)

                temp_image_name = f"slide_{slide_number}_{timestamp}.png"

                # Ensure we can write to the current directory in Cloud Run
                os.makedirs(".", exist_ok=True)

                pil_image = pdf_renderer.renderHTML(html_content, temp_image_name)

                # Add the PNG file created by HTMLRenderer to cleanup list
                temp_files_to_cleanup.append(temp_image_name)

                # Fix: Fill entire PDF page to eliminate white padding
                # Use the full page dimensions instead of preserving aspect ratio
                new_width = page_width
                new_height = page_height

                # Position at top-left corner (no centering needed)
                x = 0
                y = 0

                # Fix 3: Save as JPEG for better PDF compatibility
                temp_img_path = os.path.join(temp_dir, f"temp_slide_{slide_number}_{timestamp}.jpg")
                pil_image.save(temp_img_path, format='JPEG', quality=95)

                # Add the JPEG file to cleanup list
                temp_files_to_cleanup.append(temp_img_path)

                c.drawImage(temp_img_path, x, y, width=new_width, height=new_height)
                try:
                    os.remove(temp_img_path)
                except:
                    pass

                if i < len(req.html_slides) - 1:
                    c.showPage()

                print(f"✅ Added slide {slide_number} to PDF")

            except Exception as slide_error:
                print(f"❌ Error processing slide {i+1}: {slide_error}")
                c.setFont("Helvetica", 16)
                c.drawString(50, page_height - 100, f"Error rendering slide {i+1}")
                c.drawString(50, page_height - 130, str(slide_error))
                if i < len(req.html_slides) - 1:
                    c.showPage()

        c.save()
        print(f"✅ PDF generated: {pdf_file_path}")

        # Save PDF to Cloud Storage and get download URL
        with open(pdf_file_path, 'rb') as pdf_file:
            pdf_data = pdf_file.read()

        download_url = storage_service.save_export_file(
            current_user.uid,
            presentation_id,
            pdf_data,
            'pdf',
            pdf_filename
        )

        # Clean up all temp files created during PDF generation
        for temp_file in temp_files_to_cleanup:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    print(f"✅ Cleaned up temp file: {temp_file}")
            except Exception as cleanup_error:
                print(f"⚠️ Failed to clean up temp file {temp_file}: {cleanup_error}")

        # Clean up local PDF file
        if os.path.exists(pdf_file_path):
            os.remove(pdf_file_path)

        return PresentationResponse(
            html_slides=req.html_slides,
            presentation_js="",
            download_url=download_url,
            status="success",
            message=f"Generated PDF with {len(req.html_slides)} slides",
            total_input_tokens=0, # PDF generation itself doesn't use LLM (unless rendering to text first)
            total_output_tokens=0,
            token_details={"pdf_export": {"input_tokens": 0, "output_tokens": 0}}
        )

    except Exception as e:
        print(f"❌ An error occurred in PDF export: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/download-presentation/{filename}")
async def download_presentation(filename: str):
    try:
        # Use system temporary directory for GCP Cloud Run compatibility
        temp_dir = tempfile.gettempdir()

        # Try multiple possible locations for the file
        possible_paths = [
            os.path.join(temp_dir, filename),  # Direct in temp dir (PDF files)
            os.path.join(temp_dir, "temp_presentations", filename),  # PowerPoint files
        ]

        file_path = None
        for path in possible_paths:
            if os.path.exists(path):
                file_path = path
                break

        if not file_path:
            raise HTTPException(status_code=404, detail="Presentation file not found")

        # Determine media type based on file extension
        media_type = "application/pdf" if filename.endswith('.pdf') else "application/vnd.openxmlformats-officedocument.presentationml.presentation"

        return FileResponse(path=file_path, filename=filename, media_type=media_type)

    except Exception as e:
        print(f"❌ An error occurred during download: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# New endpoint for user dashboard
@app.get("/user-presentations")
async def get_user_presentations(current_user: FirebaseUser = Depends(get_current_user)):
    """Get list of user's presentations for dashboard"""
    try:
        presentations = storage_service.get_user_presentations(current_user.uid)
        return {
            "status": "success",
            "presentations": presentations,
            "total_count": len(presentations)
        }
    except Exception as e:
        logger.error(f"Error fetching presentations for user {current_user.uid}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch presentations")

@app.delete("/user-presentations/{presentation_id}")
async def delete_user_presentation(presentation_id: str, current_user: FirebaseUser = Depends(get_current_user)):
    """Delete a user's presentation"""
    try:
        # TODO: Implement delete functionality in CloudStorageService
        # For now, return success
        return {
            "status": "success",
            "message": f"Presentation {presentation_id} deleted successfully"
        }
    except Exception as e:
        logger.error(f"Error deleting presentation {presentation_id} for user {current_user.uid}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete presentation")

# --- End of app.py ---