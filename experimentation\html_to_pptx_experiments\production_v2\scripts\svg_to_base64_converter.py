#!/usr/bin/env python3
"""
SVG to Base64 PNG Converter
Downloads SVG files and converts them to base64-encoded PNG format for PptxGenJS compatibility
"""

import requests
import base64
import io
from PIL import Image
import cairosvg
from pathlib import Path
import json

class SVGToBase64Converter:
    """Convert SVG files to base64 PNG format for reliable PowerPoint embedding"""
    
    def __init__(self):
        self.svg_cache = {}
        self.base64_cache = {}
    
    def download_svg(self, url: str) -> str:
        """Download SVG content from URL"""
        if url in self.svg_cache:
            return self.svg_cache[url]
        
        try:
            print(f"📥 Downloading SVG: {url}")
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            svg_content = response.text
            self.svg_cache[url] = svg_content
            return svg_content
            
        except Exception as e:
            print(f"❌ Failed to download {url}: {e}")
            return None
    
    def svg_to_png_base64(self, svg_content: str, width: int = 200, height: int = 200) -> str:
        """Convert SVG content to base64-encoded PNG"""
        try:
            # Convert SVG to PNG using cairosvg
            png_data = cairosvg.svg2png(
                bytestring=svg_content.encode('utf-8'),
                output_width=width,
                output_height=height
            )
            
            # Convert to base64
            base64_string = base64.b64encode(png_data).decode('utf-8')
            return f"data:image/png;base64,{base64_string}"
            
        except Exception as e:
            print(f"❌ SVG to PNG conversion failed: {e}")
            return None
    
    def convert_url_to_base64(self, url: str, width: int = 200, height: int = 200) -> str:
        """Download SVG from URL and convert to base64 PNG"""
        cache_key = f"{url}_{width}_{height}"
        
        if cache_key in self.base64_cache:
            return self.base64_cache[cache_key]
        
        svg_content = self.download_svg(url)
        if not svg_content:
            return None
        
        base64_png = self.svg_to_png_base64(svg_content, width, height)
        if base64_png:
            self.base64_cache[cache_key] = base64_png
        
        return base64_png
    
    def process_slide_3_svgs(self):
        """Process all SVG files from slide 3 and return base64 mappings"""
        svg_urls = {
            'castle': 'https://www.svgrepo.com/show/514338/castle.svg',
            'unlocked_padlock': 'https://www.svgrepo.com/show/315900/unlocked-padlock.svg',
            'warning_icon': 'https://www.svgrepo.com/show/341475/danger-warning-sign-alert-be-careful.svg'
        }
        
        base64_mappings = {}
        
        print("🔄 Converting Slide 3 SVG files to base64 PNG...")
        
        # Castle icon (larger size)
        castle_base64 = self.convert_url_to_base64(svg_urls['castle'], 350, 350)
        if castle_base64:
            base64_mappings['castle'] = castle_base64
            print("✅ Castle icon converted")
        
        # Unlocked padlock (medium size)
        padlock_base64 = self.convert_url_to_base64(svg_urls['unlocked_padlock'], 80, 80)
        if padlock_base64:
            base64_mappings['unlocked_padlock'] = padlock_base64
            print("✅ Unlocked padlock converted")
        
        # Warning icons (small size)
        warning_base64 = self.convert_url_to_base64(svg_urls['warning_icon'], 24, 24)
        if warning_base64:
            base64_mappings['warning_icon'] = warning_base64
            print("✅ Warning icon converted")
        
        return base64_mappings
    
    def save_mappings_to_file(self, mappings: dict, output_file: str):
        """Save base64 mappings to JSON file"""
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(mappings, f, indent=2)
        
        print(f"💾 Base64 mappings saved to: {output_file}")

def main():
    """Main function to convert slide 3 SVGs"""
    converter = SVGToBase64Converter()
    
    # Process slide 3 SVGs
    mappings = converter.process_slide_3_svgs()
    
    if mappings:
        # Save to file
        output_file = "generated/svg_base64_mappings.json"
        converter.save_mappings_to_file(mappings, output_file)
        
        print(f"\n🎉 Successfully converted {len(mappings)} SVG files!")
        print("📋 Base64 mappings:")
        for name, base64_data in mappings.items():
            preview = base64_data[:50] + "..." if len(base64_data) > 50 else base64_data
            print(f"   {name}: {preview}")
    else:
        print("❌ No SVG files were successfully converted")

if __name__ == "__main__":
    main()
