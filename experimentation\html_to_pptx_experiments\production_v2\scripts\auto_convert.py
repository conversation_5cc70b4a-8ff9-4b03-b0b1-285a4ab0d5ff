#!/usr/bin/env python3
"""
Automated HTML-to-PowerPoint Conversion with Intelligent Routing
Combines pattern analysis with automatic conversion execution
"""

import sys
import os
import asyncio
import subprocess
from pathlib import Path
from typing import Dict

# Add root directory to path for proper module imports
script_dir = Path(__file__).parent
root_dir = script_dir.parent.parent.parent.parent  # Go up to workspace root
sys.path.insert(0, str(root_dir))

# Load environment variables FIRST
try:
    from dotenv import load_dotenv

    # Load environment variables
    env_paths = ["../../../../local.env", "../../../../.env", "../../../local.env", "../../../.env", "../../local.env", "../../.env", "../local.env", "../.env", "local.env", ".env"]
    env_loaded = False
    for env_path in env_paths:
        if os.path.exists(env_path):
            load_dotenv(env_path)
            print(f"✅ Environment loaded from: {env_path}")
            env_loaded = True
            break

    if not env_loaded:
        print("⚠️ No environment file found, using system environment variables")

except ImportError:
    print("⚠️ dotenv not available, using system environment variables")

# Import from local production_v2 scripts
try:
    # Add current scripts directory to path
    current_scripts_dir = script_dir
    sys.path.insert(0, str(current_scripts_dir))
    from simple_router import SimpleIntelligentRouter
    from llm_translator import ProductionLLMTranslator
except ImportError as e:
    print(f"❌ Failed to import production_v2 modules: {e}")
    sys.exit(1)

class AutoConverter:
    """
    Automated conversion system with intelligent prompt routing
    """
    
    def __init__(self, provider_mode: str = "vertex"):
        """
        Initialize AutoConverter with specified provider mode

        Args:
            provider_mode: "vertex" for Vertex AI (more reliable) or "gemini" for direct Gemini API (has thinking budget)
        """
        self.router = SimpleIntelligentRouter()
        self.translator = ProductionLLMTranslator(provider_mode=provider_mode)
        self.conversion_history = []
        self.provider_mode = provider_mode

    async def convert_single_file(self, html_file: str, force_prompt: str = None) -> bool:
        """
        Convert a single HTML file to PowerPoint using intelligent routing and production translator
        """
        try:
            # Get slide name from file
            slide_name = Path(html_file).stem

            # Load HTML content
            html_path = Path(html_file)
            if not html_path.exists():
                print(f"❌ HTML file not found: {html_file}")
                return False

            html_content = html_path.read_text(encoding='utf-8')
            print(f"🧠 Testing with: {slide_name}")
            print(f"📄 HTML content: {len(html_content)} characters")

            if force_prompt:
                optimal_prompt = force_prompt
                confidence = 100
                print(f"🔧 Using forced prompt: {force_prompt}")
            else:
                # Use the full routing logic which includes special handling for slide_3_general
                optimal_prompt, confidence = self.router.route_html_content(html_content, slide_name)
                # Also get analysis for translator
                patterns = self.router.analyze_html_patterns(html_content, slide_name)
                self.translator.last_analysis = patterns.get('analysis')
                print(f"🎯 Selected prompt: {optimal_prompt} ({confidence}%)")

            print(f"\n🔄 Converting {slide_name} with {optimal_prompt} prompt...")

            # Generate JavaScript using production translator
            js_code = await self.translator.translate_html_to_js(
                html_content=html_content,
                slide_name=slide_name,
                prompt_name=optimal_prompt
            )

            if js_code:
                print(f"✅ Translation successful: {len(js_code)} characters")

                # Save JavaScript file to individual/ subdirectory (session-based organization)
                script_dir = Path(__file__).parent
                js_dir = script_dir / "generated" / "javascript" / "individual"
                js_dir.mkdir(parents=True, exist_ok=True)
                js_file_path = js_dir / f"{slide_name}_{optimal_prompt}_auto_convert.js"

                # Create presentations directory
                presentations_dir = script_dir / "generated" / "presentations"
                presentations_dir.mkdir(parents=True, exist_ok=True)

                # Write JavaScript with executable wrapper (updated path)
                executable_js = f"""const PptxGenJS = require('pptxgenjs');

{js_code}

if (require.main === module) {{
    createPresentation()
        .then(() => console.log('✅ PowerPoint generated!'))
        .catch(error => console.error('❌ Error:', error));
}}
"""
                js_file_path.write_text(executable_js, encoding='utf-8')
                print(f"💾 Saved JavaScript: {js_file_path}")

                # Try to generate PowerPoint
                print(f"🔄 Generating PowerPoint...")

                node_result = subprocess.run(
                    ["node", str(js_file_path)],
                    capture_output=True,
                    text=True,
                    cwd=js_file_path.parent
                )

                if node_result.returncode == 0:
                    print(f"🎉 PowerPoint generated successfully!")

                    # Check if PowerPoint file was actually created
                    expected_pptx_path = presentations_dir / f"{slide_name}.pptx"
                    if expected_pptx_path.exists():
                        print(f"📄 PowerPoint file: {expected_pptx_path}")
                        file_size = expected_pptx_path.stat().st_size
                        print(f"📊 File size: {file_size:,} bytes")
                    else:
                        print(f"⚠️ PowerPoint file not found at expected location: {expected_pptx_path}")

                    # Record success
                    self.conversion_history.append({
                        'slide': slide_name,
                        'prompt': optimal_prompt,
                        'confidence': confidence,
                        'status': 'success',
                        'js_file': str(js_file_path),
                        'pptx_file': str(expected_pptx_path) if expected_pptx_path.exists() else None
                    })
                    return True
                else:
                    print(f"❌ PowerPoint generation failed:")
                    print(node_result.stderr)

                    # Record failure
                    self.conversion_history.append({
                        'slide': slide_name,
                        'prompt': optimal_prompt,
                        'confidence': confidence,
                        'status': 'powerpoint_error',
                        'error': node_result.stderr
                    })
                    return False
            else:
                print(f"❌ No JavaScript code generated")

                # Record failure
                self.conversion_history.append({
                    'slide': slide_name,
                    'prompt': optimal_prompt,
                    'confidence': confidence,
                    'status': 'translation_error',
                    'error': 'No JavaScript code generated'
                })
                return False

        except Exception as e:
            print(f"❌ Unexpected error: {e}")

            # Record failure
            self.conversion_history.append({
                'slide': slide_name if 'slide_name' in locals() else 'unknown',
                'prompt': optimal_prompt if 'optimal_prompt' in locals() else 'unknown',
                'confidence': confidence if 'confidence' in locals() else 0,
                'status': 'exception_error',
                'error': str(e)
            })
            return False
    
    async def convert_batch(self, html_directory: str = "generated_html",
                           force_prompt: str = None) -> Dict:
        """
        Convert all HTML files in a directory
        """
        print(f"🚀 Batch Conversion: {html_directory}")
        print("=" * 60)
        
        html_dir = Path(html_directory)
        if not html_dir.exists():
            print(f"❌ Directory not found: {html_directory}")
            return {}
        
        html_files = list(html_dir.glob("*.html"))
        if not html_files:
            print(f"❌ No HTML files found in: {html_directory}")
            return {}
        
        results = {}
        successful = 0
        failed = 0
        
        for html_file in sorted(html_files):
            print(f"\n" + "="*40)
            print(f"📄 Processing: {html_file.name}")
            print("="*40)
            
            success = await self.convert_single_file(str(html_file), force_prompt)
            
            if success:
                successful += 1
                results[html_file.name] = 'success'
            else:
                failed += 1
                results[html_file.name] = 'failed'
        
        # Summary
        print(f"\n" + "="*60)
        print(f"📊 BATCH CONVERSION SUMMARY")
        print("="*60)
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"📈 Success rate: {successful/(successful+failed)*100:.1f}%")
        
        # Detailed results
        print(f"\n📋 Detailed Results:")
        for file_name, status in results.items():
            status_icon = "✅" if status == 'success' else "❌"
            print(f"   {status_icon} {file_name}")
        
        # Show conversion history
        if self.conversion_history:
            print(f"\n🔍 Prompt Usage Analysis:")
            prompt_usage = {}
            for record in self.conversion_history:
                prompt = record['prompt']
                status = record['status']
                
                if prompt not in prompt_usage:
                    prompt_usage[prompt] = {'success': 0, 'failed': 0}
                
                if status == 'success':
                    prompt_usage[prompt]['success'] += 1
                else:
                    prompt_usage[prompt]['failed'] += 1
            
            for prompt, stats in prompt_usage.items():
                total = stats['success'] + stats['failed']
                success_rate = stats['success'] / total * 100 if total > 0 else 0
                print(f"   {prompt}: {stats['success']}/{total} success ({success_rate:.1f}%)")
        
        return results
    
    async def retry_failed(self, fallback_prompt: str = "balanced"):
        """
        Retry failed conversions with a fallback prompt
        """
        failed_slides = [
            record for record in self.conversion_history 
            if record['status'] != 'success'
        ]
        
        if not failed_slides:
            print("✅ No failed conversions to retry")
            return
        
        print(f"🔄 Retrying {len(failed_slides)} failed conversions with {fallback_prompt}")
        
        for record in failed_slides:
            slide_name = record['slide']
            print(f"\n🔄 Retrying {slide_name}...")
            
            # Find the HTML file
            html_file = f"generated_html/{slide_name}.html"
            if Path(html_file).exists():
                success = await self.convert_single_file(html_file, fallback_prompt)
                if success:
                    print(f"✅ Retry successful!")
                else:
                    print(f"❌ Retry failed")
            else:
                print(f"❌ HTML file not found: {html_file}")

def main():
    """CLI interface for automated conversion"""

    # Check for provider mode parameter
    provider_mode = "vertex"  # Default to Vertex AI
    args = sys.argv[1:]

    # Check if --provider is specified
    if "--provider" in args:
        provider_idx = args.index("--provider")
        if provider_idx + 1 < len(args):
            provider_mode = args[provider_idx + 1]
            # Remove --provider and its value from args
            args = args[:provider_idx] + args[provider_idx + 2:]
        else:
            print("❌ Error: --provider requires a value (vertex or gemini)")
            sys.exit(1)

    converter = AutoConverter(provider_mode=provider_mode)
    print(f"🔧 Using provider: {provider_mode.upper()}", file=sys.stderr)

    if len(args) < 1:
        print("Usage:")
        print("  python auto_convert.py <html_file> [prompt] [--provider vertex|gemini]")
        print("  python auto_convert.py --batch [directory] [prompt] [--provider vertex|gemini]")
        print("  python auto_convert.py --retry [prompt] [--provider vertex|gemini]")
        print("\nProvider Options:")
        print("  --provider vertex  # Use Vertex AI (more reliable, default)")
        print("  --provider gemini  # Use direct Gemini API (has thinking budget)")
        print("\nExamples:")
        print("  python auto_convert.py slide_8_general.html")
        print("  python auto_convert.py slide_4_general.html table_expert --provider gemini")
        print("  python auto_convert.py --batch generated_html --provider vertex")
        print("  python auto_convert.py --batch generated_html balanced --provider gemini")
        print("  python auto_convert.py --retry overflow_fix --provider vertex")
        sys.exit(1)
    
    if args[0] == "--batch":
        directory = args[1] if len(args) > 1 and not args[1].endswith('.txt') else "generated_html"
        force_prompt = args[2] if len(args) > 2 else (args[1] if len(args) > 1 and args[1].endswith('.txt') else None)

        asyncio.run(converter.convert_batch(directory, force_prompt))

    elif args[0] == "--retry":
        fallback_prompt = args[1] if len(args) > 1 else "balanced"
        asyncio.run(converter.retry_failed(fallback_prompt))

    else:
        html_file = args[0]
        force_prompt = args[1] if len(args) > 1 else None

        success = asyncio.run(converter.convert_single_file(html_file, force_prompt))

        if success:
            print(f"\n🎉 Conversion completed successfully!")
        else:
            print(f"\n❌ Conversion failed!")
            sys.exit(1)

if __name__ == "__main__":
    main()
