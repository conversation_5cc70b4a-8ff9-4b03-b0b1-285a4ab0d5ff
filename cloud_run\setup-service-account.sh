#!/bin/bash

# Setup script for Google Cloud Service Account
# Run this script to create and configure the service account for pptx-planner

PROJECT_ID="gen-lang-client-**********"
SERVICE_ACCOUNT_NAME="pptx-planner-service"
SERVICE_ACCOUNT_EMAIL="${SERVICE_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
KEY_FILE="pptx-planner-service-account-key.json"

echo "🚀 Setting up service account for pptx-planner..."

# Set the project
gcloud config set project $PROJECT_ID

# Create service account
echo "📝 Creating service account..."
gcloud iam service-accounts create $SERVICE_ACCOUNT_NAME \
    --display-name="PPTX Planner Service Account" \
    --description="Service account for PPTX Planner app with Firebase and Storage access"

# Assign Firebase Admin role
echo "🔥 Assigning Firebase Admin role..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/firebase.admin"

# Assign Storage Object Admin role
echo "☁️ Assigning Storage Object Admin role..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/storage.objectAdmin"

# Assign Storage Object Creator role (for signed URLs)
echo "🔗 Assigning Storage Object Creator role..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/storage.objectCreator"

# Create and download key
echo "🔑 Creating service account key..."
gcloud iam service-accounts keys create $KEY_FILE \
    --iam-account=$SERVICE_ACCOUNT_EMAIL

echo "✅ Service account setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Move $KEY_FILE to a secure location"
echo "2. Update local.env with the path to your key file:"
echo "   FIREBASE_SERVICE_ACCOUNT_PATH=/path/to/$KEY_FILE"
echo "3. Create the GCS bucket:"
echo "   gsutil mb gs://pptx-planner-storage"
echo "4. Set bucket permissions:"
echo "   gsutil iam ch serviceAccount:$SERVICE_ACCOUNT_EMAIL:objectAdmin gs://pptx-planner-storage"
echo ""
echo "🔒 Security Note: Keep the service account key file secure and never commit it to version control!"
